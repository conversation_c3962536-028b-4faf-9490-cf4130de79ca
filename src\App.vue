<script setup lang="ts">
import { RouterView } from 'vue-router'
import BottomNavigation from './components/BottomNavigation.vue'
</script>

<template>
  <div id="app" class="min-h-screen bg-gray-50 dark:bg-black">
    <!-- 主要内容区域 -->
    <main class="pb-16">
      <RouterView />
    </main>

    <!-- 底部导航 -->
    <BottomNavigation />
  </div>
</template>

<style>
#app {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 移动端视口设置 */
@viewport {
  width: device-width;
  initial-scale: 1.0;
  maximum-scale: 1.0;
  user-scalable: no;
}

/* 防止iOS Safari缩放 */
input[type="text"],
input[type="number"],
input[type="email"],
input[type="password"],
textarea,
select {
  font-size: 16px !important;
}
</style>
