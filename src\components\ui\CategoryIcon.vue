<template>
  <component
    :is="iconComponent"
    :class="iconClass"
  />
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { getIconComponent } from '@/utils/iconMap'

interface Props {
  /** 图标名称 */
  icon: string
  /** 图标大小类名 */
  size?: string
  /** 额外的CSS类名 */
  class?: string
}

const props = withDefaults(defineProps<Props>(), {
  size: 'w-5 h-5',
  class: ''
})

// 获取图标组件
const iconComponent = computed(() => getIconComponent(props.icon))

// 合并CSS类名
const iconClass = computed(() => {
  const classes = [props.size]
  if (props.class) {
    classes.push(props.class)
  }
  return classes.join(' ')
})
</script>
