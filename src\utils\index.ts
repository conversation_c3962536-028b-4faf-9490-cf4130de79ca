/**
 * 工具函数统一导出
 * Utility functions unified exports
 */

// 格式化工具
export * from './format'

// 验证工具
export * from './validation'

// 计算工具
export * from './calculation'

// 通用工具
export * from './common'

// 重新导出常用函数（便于使用）
export {
  // 格式化
  formatCurrency,
  formatDate,
  formatRelativeTime,
  formatNumber,
  formatPercentage,
  truncateText
} from './format'

export {
  // 验证
  isEmpty,
  isNotEmpty,
  isValidNumber,
  isPositiveNumber,
  isValidDate,
  validateTransaction,
  validateCategory
} from './validation'

export {
  // 计算
  safeAdd,
  safeSubtract,
  safeMultiply,
  safeDivide,
  calculateAverage,
  calculatePercentage,
  calculateGrowthRate,
  calculateTransactionTotal,
  calculateBalance
} from './calculation'

export {
  // 通用
  deepClone,
  deepMerge,
  uniqueArray,
  groupBy,
  debounce,
  throttle,
  generateUUID,
  sleep
} from './common'
