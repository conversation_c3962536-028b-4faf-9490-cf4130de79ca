/**
 * 通用工具函数
 * Common utility functions
 */

// ==========================================================================
// 对象操作 - Object Operations
// ==========================================================================

/**
 * 深拷贝对象
 * @param obj 要拷贝的对象
 * @returns 深拷贝后的对象
 */
export function deepClone<T>(obj: T): T {
  if (obj === null || typeof obj !== 'object') {
    return obj
  }

  if (obj instanceof Date) {
    return new Date(obj.getTime()) as unknown as T
  }

  if (obj instanceof Array) {
    return obj.map(item => deepClone(item)) as unknown as T
  }

  if (typeof obj === 'object') {
    const clonedObj = {} as T
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        clonedObj[key] = deepClone(obj[key])
      }
    }
    return clonedObj
  }

  return obj
}

/**
 * 合并对象（深度合并）
 * @param target 目标对象
 * @param sources 源对象数组
 * @returns 合并后的对象
 */
export function deepMerge<T extends Record<string, any>>(target: T, ...sources: Partial<T>[]): T {
  if (!sources.length) return target

  const source = sources.shift()
  if (!source) return target

  if (isObject(target) && isObject(source)) {
    for (const key in source) {
      if (isObject(source[key])) {
        if (!target[key]) Object.assign(target, { [key]: {} })
        deepMerge(target[key], source[key])
      } else {
        Object.assign(target, { [key]: source[key] })
      }
    }
  }

  return deepMerge(target, ...sources)
}

/**
 * 检查是否为对象
 * @param item 要检查的项
 * @returns 是否为对象
 */
export function isObject(item: any): item is Record<string, any> {
  return item && typeof item === 'object' && !Array.isArray(item)
}

/**
 * 获取对象的嵌套属性值
 * @param obj 对象
 * @param path 属性路径（如 'a.b.c'）
 * @param defaultValue 默认值
 * @returns 属性值
 */
export function getNestedValue<T = any>(
  obj: Record<string, any>,
  path: string,
  defaultValue?: T
): T {
  const keys = path.split('.')
  let result = obj

  for (const key of keys) {
    if (result === null || result === undefined || !(key in result)) {
      return defaultValue as T
    }
    result = result[key]
  }

  return result as T
}

/**
 * 设置对象的嵌套属性值
 * @param obj 对象
 * @param path 属性路径（如 'a.b.c'）
 * @param value 要设置的值
 */
export function setNestedValue(
  obj: Record<string, any>,
  path: string,
  value: any
): void {
  const keys = path.split('.')
  const lastKey = keys.pop()!
  let current = obj

  for (const key of keys) {
    if (!(key in current) || !isObject(current[key])) {
      current[key] = {}
    }
    current = current[key]
  }

  current[lastKey] = value
}

/**
 * 从对象中选择指定的属性
 * @param obj 源对象
 * @param keys 要选择的属性键数组
 * @returns 包含选择属性的新对象
 */
export function pick<T extends Record<string, any>, K extends keyof T>(
  obj: T,
  keys: K[]
): Pick<T, K> {
  const result = {} as Pick<T, K>
  keys.forEach(key => {
    if (key in obj) {
      result[key] = obj[key]
    }
  })
  return result
}

/**
 * 从对象中排除指定的属性
 * @param obj 源对象
 * @param keys 要排除的属性键数组
 * @returns 排除指定属性后的新对象
 */
export function omit<T extends Record<string, any>, K extends keyof T>(
  obj: T,
  keys: K[]
): Omit<T, K> {
  const result = { ...obj }
  keys.forEach(key => {
    delete result[key]
  })
  return result
}

// ==========================================================================
// 数组操作 - Array Operations
// ==========================================================================

/**
 * 数组去重
 * @param array 数组
 * @param keyFn 获取唯一键的函数（可选）
 * @returns 去重后的数组
 */
export function uniqueArray<T>(
  array: T[],
  keyFn?: (item: T) => any
): T[] {
  if (!keyFn) {
    return [...new Set(array)]
  }

  const seen = new Set()
  return array.filter(item => {
    const key = keyFn(item)
    if (seen.has(key)) {
      return false
    }
    seen.add(key)
    return true
  })
}

/**
 * 数组分组
 * @param array 数组
 * @param keyFn 获取分组键的函数
 * @returns 分组后的对象
 */
export function groupBy<T, K extends string | number | symbol>(
  array: T[],
  keyFn: (item: T) => K
): Record<K, T[]> {
  return array.reduce((groups, item) => {
    const key = keyFn(item)
    if (!groups[key]) {
      groups[key] = []
    }
    groups[key].push(item)
    return groups
  }, {} as Record<K, T[]>)
}

/**
 * 数组分块
 * @param array 数组
 * @param size 块大小
 * @returns 分块后的二维数组
 */
export function chunkArray<T>(array: T[], size: number): T[][] {
  const chunks: T[][] = []
  for (let i = 0; i < array.length; i += size) {
    chunks.push(array.slice(i, i + size))
  }
  return chunks
}

/**
 * 数组排序（支持多个排序条件）
 * @param array 数组
 * @param sortFns 排序函数数组
 * @returns 排序后的数组
 */
export function multiSort<T>(
  array: T[],
  ...sortFns: Array<(a: T, b: T) => number>
): T[] {
  return [...array].sort((a, b) => {
    for (const sortFn of sortFns) {
      const result = sortFn(a, b)
      if (result !== 0) {
        return result
      }
    }
    return 0
  })
}

/**
 * 数组求交集
 * @param arrays 数组的数组
 * @returns 交集数组
 */
export function intersection<T>(...arrays: T[][]): T[] {
  if (arrays.length === 0) return []
  if (arrays.length === 1) return [...arrays[0]]

  return arrays.reduce((acc, current) =>
    acc.filter(item => current.includes(item))
  )
}

/**
 * 数组求并集
 * @param arrays 数组的数组
 * @returns 并集数组
 */
export function union<T>(...arrays: T[][]): T[] {
  return uniqueArray(arrays.flat())
}

/**
 * 数组求差集
 * @param array1 第一个数组
 * @param array2 第二个数组
 * @returns 差集数组（在array1中但不在array2中的元素）
 */
export function difference<T>(array1: T[], array2: T[]): T[] {
  return array1.filter(item => !array2.includes(item))
}

// ==========================================================================
// 字符串操作 - String Operations
// ==========================================================================

/**
 * 生成随机字符串
 * @param length 长度
 * @param charset 字符集
 * @returns 随机字符串
 */
export function generateRandomString(
  length: number,
  charset: string = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
): string {
  let result = ''
  for (let i = 0; i < length; i++) {
    result += charset.charAt(Math.floor(Math.random() * charset.length))
  }
  return result
}

/**
 * 生成UUID
 * @returns UUID字符串
 */
export function generateUUID(): string {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {
    const r = Math.random() * 16 | 0
    const v = c === 'x' ? r : (r & 0x3 | 0x8)
    return v.toString(16)
  })
}

/**
 * 字符串模板替换
 * @param template 模板字符串
 * @param variables 变量对象
 * @returns 替换后的字符串
 */
export function templateReplace(
  template: string,
  variables: Record<string, any>
): string {
  return template.replace(/\{\{(\w+)\}\}/g, (match, key) => {
    return variables[key] !== undefined ? String(variables[key]) : match
  })
}

/**
 * 字符串哈希
 * @param str 字符串
 * @returns 哈希值
 */
export function stringHash(str: string): number {
  let hash = 0
  if (str.length === 0) return hash
  
  for (let i = 0; i < str.length; i++) {
    const char = str.charCodeAt(i)
    hash = ((hash << 5) - hash) + char
    hash = hash & hash // 转换为32位整数
  }
  
  return hash
}

// ==========================================================================
// 函数工具 - Function Utilities
// ==========================================================================

/**
 * 防抖函数
 * @param func 要防抖的函数
 * @param delay 延迟时间（毫秒）
 * @returns 防抖后的函数
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  delay: number
): (...args: Parameters<T>) => void {
  let timeoutId: ReturnType<typeof setTimeout>
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeoutId)
    timeoutId = setTimeout(() => func(...args), delay)
  }
}

/**
 * 节流函数
 * @param func 要节流的函数
 * @param delay 延迟时间（毫秒）
 * @returns 节流后的函数
 */
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  delay: number
): (...args: Parameters<T>) => void {
  let lastCall = 0
  
  return (...args: Parameters<T>) => {
    const now = Date.now()
    if (now - lastCall >= delay) {
      lastCall = now
      func(...args)
    }
  }
}

/**
 * 记忆化函数
 * @param func 要记忆化的函数
 * @param keyFn 生成缓存键的函数
 * @returns 记忆化后的函数
 */
export function memoize<T extends (...args: any[]) => any>(
  func: T,
  keyFn?: (...args: Parameters<T>) => string
): T {
  const cache = new Map<string, ReturnType<T>>()
  
  return ((...args: Parameters<T>) => {
    const key = keyFn ? keyFn(...args) : JSON.stringify(args)
    
    if (cache.has(key)) {
      return cache.get(key)!
    }
    
    const result = func(...args)
    cache.set(key, result)
    return result
  }) as T
}

/**
 * 重试函数
 * @param func 要重试的异步函数
 * @param maxRetries 最大重试次数
 * @param delay 重试间隔（毫秒）
 * @returns Promise
 */
export async function retry<T>(
  func: () => Promise<T>,
  maxRetries: number = 3,
  delay: number = 1000
): Promise<T> {
  let lastError: Error
  
  for (let i = 0; i <= maxRetries; i++) {
    try {
      return await func()
    } catch (error) {
      lastError = error instanceof Error ? error : new Error(String(error))
      
      if (i === maxRetries) {
        throw lastError
      }
      
      await sleep(delay)
    }
  }
  
  throw lastError!
}

// ==========================================================================
// 时间工具 - Time Utilities
// ==========================================================================

/**
 * 睡眠函数
 * @param ms 毫秒数
 * @returns Promise
 */
export function sleep(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms))
}

/**
 * 获取时间戳
 * @returns 当前时间戳（毫秒）
 */
export function getTimestamp(): number {
  return Date.now()
}

/**
 * 格式化持续时间
 * @param ms 毫秒数
 * @returns 格式化后的持续时间字符串
 */
export function formatDuration(ms: number): string {
  const seconds = Math.floor(ms / 1000)
  const minutes = Math.floor(seconds / 60)
  const hours = Math.floor(minutes / 60)
  const days = Math.floor(hours / 24)

  if (days > 0) {
    return `${days}天${hours % 24}小时`
  } else if (hours > 0) {
    return `${hours}小时${minutes % 60}分钟`
  } else if (minutes > 0) {
    return `${minutes}分钟${seconds % 60}秒`
  } else {
    return `${seconds}秒`
  }
}

// ==========================================================================
// 类型检查 - Type Checking
// ==========================================================================

/**
 * 检查是否为Promise
 * @param value 要检查的值
 * @returns 是否为Promise
 */
export function isPromise(value: any): value is Promise<any> {
  return value && typeof value.then === 'function'
}

/**
 * 检查是否为函数
 * @param value 要检查的值
 * @returns 是否为函数
 */
export function isFunction(value: any): value is Function {
  return typeof value === 'function'
}

/**
 * 检查是否为空值
 * @param value 要检查的值
 * @returns 是否为空值
 */
export function isNullOrUndefined(value: any): value is null | undefined {
  return value === null || value === undefined
}

/**
 * 检查是否为原始类型
 * @param value 要检查的值
 * @returns 是否为原始类型
 */
export function isPrimitive(value: any): boolean {
  return value !== Object(value)
}
