<template>
  <div class="flex flex-col h-screen">
    <!-- 固定标题栏 -->
    <div class="fixed top-0 left-0 right-0 bg-white dark:bg-black border-b border-gray-200 dark:border-gray-800 px-4 py-4 safe-area-top z-30">
      <h1 class="text-lg font-semibold text-center">{{ $t('settings.title') }}</h1>
    </div>

    <!-- 内容区域（不可滚动） -->
    <div class="flex-1 px-4 py-6 pt-20 pb-20 safe-area-top">


      <!-- 数据设置 -->
      <div class="mb-8">
        <h3 class="text-lg font-semibold mb-4">{{ $t('settings.data') }}</h3>
        <BaseCard padding="none">


          <!-- 数据导出 -->
          <button
            @click="exportData"
            class="w-full p-4 border-b border-gray-200 dark:border-gray-700 last:border-b-0 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors text-left first:rounded-t-2xl last:rounded-b-2xl last:border-b-0"
          >
            <div class="flex items-center justify-between">
              <div class="flex items-center">
                <div class="w-10 h-10 flex items-center justify-center mr-3">
                  <ArrowDownTrayIcon class="w-6 h-6 text-blue-500" />
                </div>
                <div>
                  <p class="font-medium">{{ $t('settings.export') }}</p>
                  <p class="text-sm text-gray-500 dark:text-gray-400">
                    {{ $t('settings.exportDescription') }}
                  </p>
                </div>
              </div>
              <ChevronRightIcon class="w-5 h-5 text-gray-400" />
            </div>
          </button>

          <!-- 数据导入 -->
          <button
            @click="triggerFileInput"
            class="w-full p-4 border-b border-gray-200 dark:border-gray-700 last:border-b-0 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors text-left first:rounded-t-2xl last:rounded-b-2xl last:border-b-0"
          >
            <div class="flex items-center justify-between">
              <div class="flex items-center">
                <div class="w-10 h-10 flex items-center justify-center mr-3">
                  <ArrowUpTrayIcon class="w-6 h-6 text-green-500" />
                </div>
                <div>
                  <p class="font-medium">{{ $t('settings.import') }}</p>
                  <p class="text-sm text-gray-500 dark:text-gray-400">
                    {{ $t('settings.importDescription') }}
                  </p>
                </div>
              </div>
              <ChevronRightIcon class="w-5 h-5 text-gray-400" />
            </div>
          </button>

          <!-- 隐藏的文件输入 -->
          <input
            ref="fileInput"
            type="file"
            accept=".json"
            @change="handleFileSelect"
            class="hidden"
          />

          <!-- 数据清除 -->
          <button
            @click="clearAllData"
            class="w-full p-4 border-b border-gray-200 dark:border-gray-700 last:border-b-0 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors text-left first:rounded-t-2xl last:rounded-b-2xl last:border-b-0"
          >
            <div class="flex items-center justify-between">
              <div class="flex items-center">
                <div class="w-10 h-10 flex items-center justify-center mr-3">
                  <TrashIcon class="w-6 h-6 text-red-500" />
                </div>
                <div>
                  <p class="font-medium text-red-500">{{ $t('settings.clear') }}</p>
                  <p class="text-sm text-gray-500 dark:text-gray-400">
                    {{ $t('settings.clearDescription') }}
                  </p>
                </div>
              </div>
              <ChevronRightIcon class="w-5 h-5 text-gray-400" />
            </div>
          </button>
        </BaseCard>
      </div>

      <!-- 关于 -->
      <div class="mb-8">
        <h3 class="text-lg font-semibold mb-4">{{ $t('settings.about') }}</h3>
        <BaseCard padding="none">
          <div class="p-4 border-b border-gray-200 dark:border-gray-700 last:border-b-0">
            <div class="flex items-center">
              <div class="w-10 h-10 flex items-center justify-center mr-3">
                <InformationCircleIcon class="w-6 h-6 text-gray-500" />
              </div>
              <div>
                <p class="font-medium">Money Tracker</p>
                <p class="text-sm text-gray-500 dark:text-gray-400">
                  {{ $t('settings.version') }} 1.0.0
                </p>
              </div>
            </div>
          </div>
        </BaseCard>
      </div>
    </div>



    <!-- 导入数据确认弹窗 -->
    <BaseModal
      v-model="showImportConfirm"
      :title="$t('settings.confirmImportTitle')"
      size="md"
    >
      <p class="text-gray-600 dark:text-gray-400 mb-4">
        {{ $t('settings.confirmImportMessage') }}
      </p>

      <!-- 导入文件信息 -->
      <div v-if="importData" class="bg-gray-50 dark:bg-gray-800 rounded-lg p-4 mb-4 text-sm">
        <div class="mb-3">
          <h4 class="font-medium text-gray-900 dark:text-gray-100 mb-2">{{ $t('settings.importFileInfo') }}</h4>
        </div>
        <div class="grid grid-cols-2 gap-4">
          <div class="space-y-2">
            <div class="flex justify-between">
              <span class="text-gray-600 dark:text-gray-400">{{ $t('settings.transactions') }}:</span>
              <span class="font-medium text-blue-600 dark:text-blue-400">{{ importData.transactions?.length || 0 }}</span>
            </div>
            <div class="flex justify-between">
              <span class="text-gray-600 dark:text-gray-400">{{ $t('settings.categories') }}:</span>
              <span class="font-medium text-green-600 dark:text-green-400">{{ importData.categories?.length || 0 }}</span>
            </div>
          </div>
          <div class="space-y-2">
            <div class="flex justify-between">
              <span class="text-gray-600 dark:text-gray-400">{{ $t('settings.exportDate') }}:</span>
              <span class="font-medium text-gray-900 dark:text-gray-100">{{ formatImportDate(importData.exportDate) }}</span>
            </div>
            <div class="flex justify-between" v-if="importData.settings">
              <span class="text-gray-600 dark:text-gray-400">{{ $t('settings.settings') }}:</span>
              <span class="font-medium text-purple-600 dark:text-purple-400">{{ $t('common.included') }}</span>
            </div>
          </div>
        </div>

        <!-- 当前数据警告 -->
        <div class="mt-3 pt-3 border-t border-gray-200 dark:border-gray-700">
          <div class="flex items-start space-x-2">
            <div class="w-4 h-4 text-amber-500 mt-0.5">⚠️</div>
            <div class="text-xs text-amber-600 dark:text-amber-400">
              {{ $t('settings.currentDataWarning') }}
            </div>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="flex space-x-3">
          <button
            @click="showImportConfirm = false"
            class="flex-1 btn btn-secondary"
          >
            {{ $t('common.cancel') }}
          </button>
          <button
            @click="confirmImport"
            :disabled="isImporting"
            class="flex-1 btn btn-primary"
            :class="{ 'opacity-50 cursor-not-allowed': isImporting }"
          >
            {{ isImporting ? $t('common.loading') : $t('settings.confirmImport') }}
          </button>
        </div>
      </template>
    </BaseModal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import {
  ArrowDownTrayIcon,
  ArrowUpTrayIcon,
  TrashIcon,
  InformationCircleIcon,
  ChevronRightIcon
} from '@heroicons/vue/24/outline'
import { BaseCard, BaseModal } from '@/components/ui'
import { useSettingsStore } from '@/stores/settings'
import { useTransactionStore } from '@/stores/transactions'
import { useI18n } from 'vue-i18n'


const settingsStore = useSettingsStore()
const transactionStore = useTransactionStore()
const { t, locale } = useI18n()

// State
const showImportConfirm = ref(false)
const fileInput = ref<HTMLInputElement>()
const importData = ref<any>(null)
const isImporting = ref(false)





// Methods

function exportData() {
  const data = {
    transactions: transactionStore.transactions,
    categories: transactionStore.categories,
    settings: settingsStore.settings,
    exportDate: new Date().toISOString()
  }

  const blob = new Blob([JSON.stringify(data, null, 2)], {
    type: 'application/json'
  })

  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `money-tracker-export-${new Date().toISOString().split('T')[0]}.json`
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
  URL.revokeObjectURL(url)
}

function clearAllData() {
  // Clear all data
  localStorage.clear()

  // Reload page
  window.location.reload()
}

function triggerFileInput() {
  fileInput.value?.click()
}

function handleFileSelect(event: Event) {
  const target = event.target as HTMLInputElement
  const file = target.files?.[0]

  if (!file) return

  const reader = new FileReader()
  reader.onload = (e) => {
    try {
      const data = JSON.parse(e.target?.result as string)

      // 验证数据格式
      if (validateImportData(data)) {
        importData.value = data
        showImportConfirm.value = true
      } else {
        alert(t('settings.importFormatError'))
      }
    } catch (error) {
      alert(t('settings.importParseError'))
    }
  }

  reader.readAsText(file)

  // 清空文件输入，允许重复选择同一文件
  target.value = ''
}

function validateImportData(data: any): boolean {
  // 检查必要的字段
  if (!data || typeof data !== 'object') {
    return false
  }

  // 检查是否有必要的数组字段
  if (!Array.isArray(data.transactions) || !Array.isArray(data.categories)) {
    return false
  }

  // 检查导出日期
  if (!data.exportDate) {
    return false
  }

  // 验证交易记录格式（如果有的话）
  if (data.transactions.length > 0) {
    const sampleTransaction = data.transactions[0]
    if (!sampleTransaction.id || !sampleTransaction.type || !sampleTransaction.amount || !sampleTransaction.categoryId) {
      return false
    }
  }

  // 验证分类格式（如果有的话）
  if (data.categories.length > 0) {
    const sampleCategory = data.categories[0]
    if (!sampleCategory.id || !sampleCategory.name || !sampleCategory.type) {
      return false
    }
  }

  return true
}

async function confirmImport() {
  if (!importData.value || isImporting.value) return

  isImporting.value = true

  try {
    // 清除现有数据
    localStorage.clear()

    // 导入新数据
    const data = importData.value

    // 保存交易数据
    if (data.transactions && data.transactions.length > 0) {
      localStorage.setItem('transactions', JSON.stringify(data.transactions))
    }

    // 保存分类数据
    if (data.categories && data.categories.length > 0) {
      localStorage.setItem('categories', JSON.stringify(data.categories))
    }

    // 保存设置数据
    if (data.settings) {
      localStorage.setItem('app-settings', JSON.stringify(data.settings))
    }

    // 保存语言设置
    if (data.settings?.language) {
      localStorage.setItem('locale', data.settings.language)
    }

    // 显示成功消息
    alert(t('settings.importSuccess'))

    // 重新加载页面以应用新数据
    window.location.reload()

  } catch (error) {
    console.error('Import failed:', error)
    alert(t('settings.importFailed'))
    isImporting.value = false
  }

  showImportConfirm.value = false
  importData.value = null
}

function formatImportDate(dateString: string): string {
  if (!dateString) return t('common.unknown')

  try {
    const date = new Date(dateString)
    const localeMap = {
      'zh': 'zh-CN',
      'en': 'en-US'
    }
    const currentLocale = localeMap[locale.value as keyof typeof localeMap] || 'en-US'

    return date.toLocaleDateString(currentLocale, {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  } catch (error) {
    return t('common.unknown')
  }
}
</script>


