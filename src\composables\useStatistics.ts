import { computed, ref } from 'vue'
import { useTransactionStore } from '@/stores/transactions'
import type { Transaction, TransactionType, CategoryStats, MonthlyStats } from '@/types'

/**
 * 统计数据管理组合式函数
 * 提供各种统计分析功能
 */
export function useStatistics() {
  const store = useTransactionStore()

  // ==========================================================================
  // 响应式状态 - Reactive State
  // ==========================================================================

  /** 统计时间范围 */
  const dateRange = ref<{
    start?: string
    end?: string
  }>({})

  /** 统计类型筛选 */
  const typeFilter = ref<TransactionType | 'all'>('all')

  // ==========================================================================
  // 计算属性 - Computed Properties
  // ==========================================================================

  /** 筛选后的交易记录 */
  const filteredTransactions = computed(() => {
    let transactions = store.transactions

    // 按类型筛选
    if (typeFilter.value !== 'all') {
      transactions = transactions.filter(t => t.type === typeFilter.value)
    }

    // 按日期范围筛选
    if (dateRange.value.start || dateRange.value.end) {
      transactions = transactions.filter(t => {
        const transactionDate = t.date
        if (dateRange.value.start && transactionDate < dateRange.value.start) {
          return false
        }
        if (dateRange.value.end && transactionDate > dateRange.value.end) {
          return false
        }
        return true
      })
    }

    return transactions
  })

  /** 总体统计 */
  const overallStats = computed(() => {
    const transactions = filteredTransactions.value
    
    const income = transactions
      .filter(t => t.type === 'income')
      .reduce((sum, t) => sum + t.amount, 0)
    
    const expense = transactions
      .filter(t => t.type === 'expense')
      .reduce((sum, t) => sum + t.amount, 0)

    return {
      totalIncome: income,
      totalExpense: expense,
      balance: income - expense,
      transactionCount: transactions.length,
      incomeCount: transactions.filter(t => t.type === 'income').length,
      expenseCount: transactions.filter(t => t.type === 'expense').length,
      averageIncome: transactions.filter(t => t.type === 'income').length > 0 
        ? income / transactions.filter(t => t.type === 'income').length 
        : 0,
      averageExpense: transactions.filter(t => t.type === 'expense').length > 0 
        ? expense / transactions.filter(t => t.type === 'expense').length 
        : 0
    }
  })

  /** 当前月份统计 */
  const currentMonthStats = computed(() => {
    const currentMonth = new Date().toISOString().substring(0, 7)
    return getMonthlyStats(currentMonth)
  })

  /** 上个月统计 */
  const lastMonthStats = computed(() => {
    const lastMonth = new Date()
    lastMonth.setMonth(lastMonth.getMonth() - 1)
    const lastMonthString = lastMonth.toISOString().substring(0, 7)
    return getMonthlyStats(lastMonthString)
  })

  /** 月度对比 */
  const monthlyComparison = computed(() => {
    const current = currentMonthStats.value
    const last = lastMonthStats.value

    const incomeChange = last.totalIncome > 0 
      ? ((current.totalIncome - last.totalIncome) / last.totalIncome) * 100 
      : 0

    const expenseChange = last.totalExpense > 0 
      ? ((current.totalExpense - last.totalExpense) / last.totalExpense) * 100 
      : 0

    return {
      incomeChange,
      expenseChange,
      incomeDirection: incomeChange > 0 ? 'up' : incomeChange < 0 ? 'down' : 'same',
      expenseDirection: expenseChange > 0 ? 'up' : expenseChange < 0 ? 'down' : 'same'
    }
  })

  // ==========================================================================
  // 统计方法 - Statistics Methods
  // ==========================================================================

  /** 获取月度统计 */
  function getMonthlyStats(monthString: string): MonthlyStats {
    return store.getMonthlyStats(monthString)
  }

  /** 获取分类统计 */
  function getCategoryStats(type: TransactionType, monthString?: string): CategoryStats[] {
    return store.getCategoryStats(type, monthString)
  }

  /** 获取最近几个月的统计 */
  function getRecentMonthsStats(months: number = 6): MonthlyStats[] {
    const stats: MonthlyStats[] = []
    const currentDate = new Date()

    for (let i = months - 1; i >= 0; i--) {
      const date = new Date(currentDate)
      date.setMonth(date.getMonth() - i)
      const monthString = date.toISOString().substring(0, 7)
      stats.push(getMonthlyStats(monthString))
    }

    return stats
  }

  /** 获取年度统计 */
  function getYearlyStats(year: number) {
    const yearString = year.toString()
    const transactions = store.transactions.filter(t => 
      t.date.startsWith(yearString)
    )

    const monthlyData: Record<string, MonthlyStats> = {}
    
    // 初始化12个月的数据
    for (let month = 1; month <= 12; month++) {
      const monthString = `${year}-${month.toString().padStart(2, '0')}`
      monthlyData[monthString] = getMonthlyStats(monthString)
    }

    const totalIncome = Object.values(monthlyData)
      .reduce((sum, month) => sum + month.totalIncome, 0)
    
    const totalExpense = Object.values(monthlyData)
      .reduce((sum, month) => sum + month.totalExpense, 0)

    return {
      year,
      totalIncome,
      totalExpense,
      balance: totalIncome - totalExpense,
      transactionCount: transactions.length,
      monthlyData,
      averageMonthlyIncome: totalIncome / 12,
      averageMonthlyExpense: totalExpense / 12
    }
  }

  /** 获取日统计 */
  function getDailyStats(dateString: string) {
    const transactions = store.transactions.filter(t => t.date === dateString)
    
    const income = transactions
      .filter(t => t.type === 'income')
      .reduce((sum, t) => sum + t.amount, 0)
    
    const expense = transactions
      .filter(t => t.type === 'expense')
      .reduce((sum, t) => sum + t.amount, 0)

    return {
      date: dateString,
      totalIncome: income,
      totalExpense: expense,
      balance: income - expense,
      transactionCount: transactions.length,
      transactions
    }
  }

  /** 获取周统计 */
  function getWeeklyStats(startDate: string) {
    const start = new Date(startDate)
    const end = new Date(start)
    end.setDate(end.getDate() + 6)

    const startString = start.toISOString().substring(0, 10)
    const endString = end.toISOString().substring(0, 10)

    const transactions = store.transactions.filter(t => 
      t.date >= startString && t.date <= endString
    )

    const income = transactions
      .filter(t => t.type === 'income')
      .reduce((sum, t) => sum + t.amount, 0)
    
    const expense = transactions
      .filter(t => t.type === 'expense')
      .reduce((sum, t) => sum + t.amount, 0)

    // 按天分组
    const dailyData: Record<string, any> = {}
    for (let i = 0; i < 7; i++) {
      const date = new Date(start)
      date.setDate(date.getDate() + i)
      const dateString = date.toISOString().substring(0, 10)
      dailyData[dateString] = getDailyStats(dateString)
    }

    return {
      startDate: startString,
      endDate: endString,
      totalIncome: income,
      totalExpense: expense,
      balance: income - expense,
      transactionCount: transactions.length,
      dailyData,
      averageDailyIncome: income / 7,
      averageDailyExpense: expense / 7
    }
  }

  /** 获取趋势数据 */
  function getTrendData(period: 'week' | 'month' | 'year' = 'month', count: number = 12) {
    const data: Array<{
      period: string
      income: number
      expense: number
      balance: number
      transactionCount: number
    }> = []

    const currentDate = new Date()

    for (let i = count - 1; i >= 0; i--) {
      let periodString: string
      let stats: any

      if (period === 'month') {
        const date = new Date(currentDate)
        date.setMonth(date.getMonth() - i)
        periodString = date.toISOString().substring(0, 7)
        stats = getMonthlyStats(periodString)
      } else if (period === 'week') {
        const date = new Date(currentDate)
        date.setDate(date.getDate() - (i * 7))
        // 获取周一
        const monday = new Date(date)
        monday.setDate(date.getDate() - date.getDay() + 1)
        periodString = monday.toISOString().substring(0, 10)
        stats = getWeeklyStats(periodString)
      } else {
        // year
        const year = currentDate.getFullYear() - i
        periodString = year.toString()
        stats = getYearlyStats(year)
      }

      data.push({
        period: periodString,
        income: stats.totalIncome,
        expense: stats.totalExpense,
        balance: stats.balance || (stats.totalIncome - stats.totalExpense),
        transactionCount: stats.transactionCount
      })
    }

    return data
  }

  /** 获取支出排行榜 */
  function getTopExpenseCategories(limit: number = 10, monthString?: string) {
    const categoryStats = getCategoryStats('expense', monthString)
    return categoryStats
      .sort((a, b) => b.totalAmount - a.totalAmount)
      .slice(0, limit)
  }

  /** 获取收入排行榜 */
  function getTopIncomeCategories(limit: number = 10, monthString?: string) {
    const categoryStats = getCategoryStats('income', monthString)
    return categoryStats
      .sort((a, b) => b.totalAmount - a.totalAmount)
      .slice(0, limit)
  }

  // ==========================================================================
  // 筛选方法 - Filter Methods
  // ==========================================================================

  /** 设置日期范围 */
  function setDateRange(start?: string, end?: string) {
    dateRange.value = { start, end }
  }

  /** 设置类型筛选 */
  function setTypeFilter(type: TransactionType | 'all') {
    typeFilter.value = type
  }

  /** 清除筛选条件 */
  function clearFilters() {
    dateRange.value = {}
    typeFilter.value = 'all'
  }

  /** 设置预设时间范围 */
  function setPresetRange(preset: 'today' | 'week' | 'month' | 'quarter' | 'year') {
    const today = new Date()
    const todayString = today.toISOString().substring(0, 10)

    switch (preset) {
      case 'today':
        setDateRange(todayString, todayString)
        break
      case 'week':
        const weekStart = new Date(today)
        weekStart.setDate(today.getDate() - today.getDay() + 1)
        setDateRange(weekStart.toISOString().substring(0, 10), todayString)
        break
      case 'month':
        const monthStart = new Date(today.getFullYear(), today.getMonth(), 1)
        setDateRange(monthStart.toISOString().substring(0, 10), todayString)
        break
      case 'quarter':
        const quarterStart = new Date(today.getFullYear(), Math.floor(today.getMonth() / 3) * 3, 1)
        setDateRange(quarterStart.toISOString().substring(0, 10), todayString)
        break
      case 'year':
        const yearStart = new Date(today.getFullYear(), 0, 1)
        setDateRange(yearStart.toISOString().substring(0, 10), todayString)
        break
    }
  }

  // ==========================================================================
  // 返回接口 - Return Interface
  // ==========================================================================

  return {
    // 响应式状态
    dateRange,
    typeFilter,

    // 计算属性
    filteredTransactions,
    overallStats,
    currentMonthStats,
    lastMonthStats,
    monthlyComparison,

    // 统计方法
    getMonthlyStats,
    getCategoryStats,
    getRecentMonthsStats,
    getYearlyStats,
    getDailyStats,
    getWeeklyStats,
    getTrendData,
    getTopExpenseCategories,
    getTopIncomeCategories,

    // 筛选方法
    setDateRange,
    setTypeFilter,
    clearFilters,
    setPresetRange
  }
}
