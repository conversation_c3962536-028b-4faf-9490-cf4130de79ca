<template>
  <button
    :type="type"
    :disabled="disabled || loading"
    :class="buttonClasses"
    :aria-label="ariaLabel"
    :aria-describedby="ariaDescribedby"
    @click="handleClick"
    @focus="handleFocus"
    @blur="handleBlur"
  >
    <!-- 加载状态 -->
    <div
      v-if="loading"
      class="animate-spin border-2 border-current border-t-transparent rounded-full"
      :class="loadingIconClasses"
    ></div>

    <!-- 图标 -->
    <component
      v-if="icon && !loading"
      :is="icon"
      :class="iconClasses"
    />

    <!-- 文本内容 -->
    <span
      v-if="$slots.default && !iconOnly"
      :class="textClasses"
    >
      <slot />
    </span>

    <!-- 右侧图标 -->
    <component
      v-if="suffixIcon && !loading"
      :is="suffixIcon"
      :class="suffixIconClasses"
    />
  </button>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import type { Component } from 'vue'
import type { ButtonProps } from '@/types/components'

interface Props extends /* @vue-ignore */ ButtonProps {
  /** 后缀图标 */
  suffixIcon?: string | Component
  /** 无障碍标签 */
  ariaLabel?: string
  /** 无障碍描述 */
  ariaDescribedby?: string
}

const props = withDefaults(defineProps<Props>(), {
  variant: 'filled',
  color: 'primary',
  size: 'medium',
  type: 'button',
  disabled: false,
  loading: false,
  block: false,
  round: false,
  iconPosition: 'left'
})

const emit = defineEmits<{
  click: [event: MouseEvent]
  focus: [event: FocusEvent]
  blur: [event: FocusEvent]
}>()

// 计算按钮样式类
const buttonClasses = computed(() => {
  const classes = [
    'btn',
    `btn-${props.variant}`,
    `btn-${props.color}`,
    `btn-${props.size}`,
    {
      'btn-block': props.block,
      'btn-round': props.round,
      'btn-icon-only': props.icon && !$slots.default,
      'btn-loading': props.loading,
      'btn-disabled': props.disabled,
      'cursor-not-allowed': props.disabled,
      'pointer-events-none': props.loading
    }
  ]

  return classes
})

// 计算图标样式类
const iconClasses = computed(() => {
  const baseClasses = ['btn-icon']

  // 图标大小
  switch (props.size) {
    case 'small':
      baseClasses.push('w-4 h-4')
      break
    case 'large':
      baseClasses.push('w-6 h-6')
      break
    default:
      baseClasses.push('w-5 h-5')
  }

  // 图标位置
  if ($slots.default && !props.loading) {
    if (props.iconPosition === 'right') {
      baseClasses.push('ml-2')
    } else {
      baseClasses.push('mr-2')
    }
  }

  return baseClasses
})

// 计算后缀图标样式类
const suffixIconClasses = computed(() => {
  const baseClasses = ['btn-suffix-icon']

  switch (props.size) {
    case 'small':
      baseClasses.push('w-4 h-4')
      break
    case 'large':
      baseClasses.push('w-6 h-6')
      break
    default:
      baseClasses.push('w-5 h-5')
  }

  if ($slots.default) {
    baseClasses.push('ml-2')
  }

  return baseClasses
})

// 计算加载图标样式类
const loadingIconClasses = computed(() => {
  const baseClasses = []

  switch (props.size) {
    case 'small':
      baseClasses.push('w-4 h-4')
      break
    case 'large':
      baseClasses.push('w-6 h-6')
      break
    default:
      baseClasses.push('w-5 h-5')
  }

  if ($slots.default) {
    baseClasses.push('mr-2')
  }

  return baseClasses
})

// 计算文本样式类
const textClasses = computed(() => {
  const classes = ['btn-text']

  if (props.icon && !props.loading) {
    if (props.iconPosition === 'right') {
      classes.push('mr-2')
    } else {
      classes.push('ml-2')
    }
  }

  return classes
})

// 事件处理
const handleClick = (event: MouseEvent) => {
  if (!props.disabled && !props.loading) {
    emit('click', event)
  }
}

const handleFocus = (event: FocusEvent) => {
  emit('focus', event)
}

const handleBlur = (event: FocusEvent) => {
  emit('blur', event)
}
</script>

<style scoped>
/* 基础按钮样式 */
.btn {
  @apply inline-flex items-center justify-center font-medium transition-all duration-200 ease-in-out;
  @apply focus:outline-none focus:ring-2 focus:ring-offset-2;
  @apply disabled:cursor-not-allowed disabled:opacity-50;
}

/* 按钮大小 */
.btn-small {
  @apply px-3 py-1.5 text-sm rounded-md;
}

.btn-medium {
  @apply px-4 py-2 text-base rounded-lg;
}

.btn-large {
  @apply px-6 py-3 text-lg rounded-lg;
}

/* 按钮变体 */
.btn-filled {
  @apply text-white border border-transparent;
}

.btn-outlined {
  @apply bg-transparent border-2;
}

.btn-text {
  @apply bg-transparent border-none;
}

.btn-ghost {
  @apply bg-transparent border-none;
}

/* 按钮颜色 - Primary */
.btn-filled.btn-primary {
  @apply bg-blue-600 hover:bg-blue-700 focus:ring-blue-500;
}

.btn-outlined.btn-primary {
  @apply border-blue-600 text-blue-600 hover:bg-blue-50 focus:ring-blue-500;
  @apply dark:border-blue-400 dark:text-blue-400 dark:hover:bg-blue-900/20;
}

.btn-text.btn-primary,
.btn-ghost.btn-primary {
  @apply text-blue-600 hover:bg-blue-50 focus:ring-blue-500;
  @apply dark:text-blue-400 dark:hover:bg-blue-900/20;
}

/* 按钮颜色 - Secondary */
.btn-filled.btn-secondary {
  @apply bg-gray-600 hover:bg-gray-700 focus:ring-gray-500;
}

.btn-outlined.btn-secondary {
  @apply border-gray-600 text-gray-600 hover:bg-gray-50 focus:ring-gray-500;
  @apply dark:border-gray-400 dark:text-gray-400 dark:hover:bg-gray-800;
}

.btn-text.btn-secondary,
.btn-ghost.btn-secondary {
  @apply text-gray-600 hover:bg-gray-50 focus:ring-gray-500;
  @apply dark:text-gray-400 dark:hover:bg-gray-800;
}

/* 按钮颜色 - Success */
.btn-filled.btn-success {
  @apply bg-green-600 hover:bg-green-700 focus:ring-green-500;
}

.btn-outlined.btn-success {
  @apply border-green-600 text-green-600 hover:bg-green-50 focus:ring-green-500;
  @apply dark:border-green-400 dark:text-green-400 dark:hover:bg-green-900/20;
}

.btn-text.btn-success,
.btn-ghost.btn-success {
  @apply text-green-600 hover:bg-green-50 focus:ring-green-500;
  @apply dark:text-green-400 dark:hover:bg-green-900/20;
}

/* 按钮颜色 - Warning */
.btn-filled.btn-warning {
  @apply bg-yellow-600 hover:bg-yellow-700 focus:ring-yellow-500;
}

.btn-outlined.btn-warning {
  @apply border-yellow-600 text-yellow-600 hover:bg-yellow-50 focus:ring-yellow-500;
  @apply dark:border-yellow-400 dark:text-yellow-400 dark:hover:bg-yellow-900/20;
}

.btn-text.btn-warning,
.btn-ghost.btn-warning {
  @apply text-yellow-600 hover:bg-yellow-50 focus:ring-yellow-500;
  @apply dark:text-yellow-400 dark:hover:bg-yellow-900/20;
}

/* 按钮颜色 - Error */
.btn-filled.btn-error {
  @apply bg-red-600 hover:bg-red-700 focus:ring-red-500;
}

.btn-outlined.btn-error {
  @apply border-red-600 text-red-600 hover:bg-red-50 focus:ring-red-500;
  @apply dark:border-red-400 dark:text-red-400 dark:hover:bg-red-900/20;
}

.btn-text.btn-error,
.btn-ghost.btn-error {
  @apply text-red-600 hover:bg-red-50 focus:ring-red-500;
  @apply dark:text-red-400 dark:hover:bg-red-900/20;
}

/* 按钮状态 */
.btn-block {
  @apply w-full;
}

.btn-round {
  @apply rounded-full;
}

.btn-icon-only {
  @apply p-2;
}

.btn-loading {
  @apply cursor-wait;
}

.btn-disabled {
  @apply cursor-not-allowed opacity-50;
}

/* 暗色模式适配 */
.dark .btn-filled {
  @apply border-gray-600;
}
</style>
