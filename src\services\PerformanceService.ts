/**
 * 性能监控服务
 * Performance monitoring service
 */

import { BaseService } from './base/BaseService'
import type { ServiceResponse } from '@/types'

/**
 * 性能指标接口
 */
export interface PerformanceMetrics {
  /** 页面加载时间 */
  pageLoadTime: number
  /** 首次内容绘制时间 */
  firstContentfulPaint: number
  /** 最大内容绘制时间 */
  largestContentfulPaint: number
  /** 首次输入延迟 */
  firstInputDelay: number
  /** 累积布局偏移 */
  cumulativeLayoutShift: number
  /** 内存使用情况 */
  memoryUsage?: {
    used: number
    total: number
    limit: number
  }
  /** 网络信息 */
  networkInfo?: {
    effectiveType: string
    downlink: number
    rtt: number
  }
}

/**
 * 性能监控配置
 */
export interface PerformanceConfig {
  /** 是否启用监控 */
  enabled: boolean
  /** 采样率 (0-1) */
  sampleRate: number
  /** 是否监控内存 */
  monitorMemory: boolean
  /** 是否监控网络 */
  monitorNetwork: boolean
  /** 上报间隔 (毫秒) */
  reportInterval: number
  /** 最大缓存条目数 */
  maxCacheSize: number
}

/**
 * 性能监控服务类
 */
export class PerformanceService extends BaseService {
  private static instance: PerformanceService
  private config: PerformanceConfig
  private metrics: PerformanceMetrics[] = []
  private observer?: PerformanceObserver
  private memoryTimer?: number
  private networkTimer?: number

  private constructor() {
    super()
    this.config = {
      enabled: true,
      sampleRate: 1.0,
      monitorMemory: true,
      monitorNetwork: true,
      reportInterval: 30000, // 30秒
      maxCacheSize: 100
    }
  }

  /**
   * 获取单例实例
   */
  static getInstance(): PerformanceService {
    if (!PerformanceService.instance) {
      PerformanceService.instance = new PerformanceService()
    }
    return PerformanceService.instance
  }

  /**
   * 初始化性能监控
   */
  async initialize(config?: Partial<PerformanceConfig>): Promise<ServiceResponse<void>> {
    try {
      if (config) {
        this.config = { ...this.config, ...config }
      }

      if (!this.config.enabled) {
        return this.success(undefined)
      }

      // 检查浏览器支持
      if (!this.isSupported()) {
        return this.error('PERFORMANCE_NOT_SUPPORTED', '浏览器不支持性能监控API')
      }

      // 设置性能观察器
      this.setupPerformanceObserver()

      // 监控内存使用
      if (this.config.monitorMemory) {
        this.startMemoryMonitoring()
      }

      // 监控网络状态
      if (this.config.monitorNetwork) {
        this.startNetworkMonitoring()
      }

      // 收集初始指标
      await this.collectInitialMetrics()

      return this.success(undefined)
    } catch (error) {
      return this.handleError(error, 'PERFORMANCE_INIT_ERROR')
    }
  }

  /**
   * 检查浏览器支持
   */
  private isSupported(): boolean {
    return (
      typeof window !== 'undefined' &&
      'performance' in window &&
      'PerformanceObserver' in window
    )
  }

  /**
   * 设置性能观察器
   */
  private setupPerformanceObserver(): void {
    if (!window.PerformanceObserver) return

    this.observer = new PerformanceObserver((list) => {
      const entries = list.getEntries()
      this.processPerformanceEntries(entries)
    })

    // 观察各种性能指标
    try {
      this.observer.observe({ entryTypes: ['navigation', 'paint', 'largest-contentful-paint', 'first-input'] })
    } catch (error) {
      console.warn('Failed to observe performance entries:', error)
    }
  }

  /**
   * 处理性能条目
   */
  private processPerformanceEntries(entries: PerformanceEntry[]): void {
    entries.forEach(entry => {
      switch (entry.entryType) {
        case 'navigation':
          this.handleNavigationEntry(entry as PerformanceNavigationTiming)
          break
        case 'paint':
          this.handlePaintEntry(entry as PerformancePaintTiming)
          break
        case 'largest-contentful-paint':
          this.handleLCPEntry(entry as any)
          break
        case 'first-input':
          this.handleFIDEntry(entry as any)
          break
      }
    })
  }

  /**
   * 处理导航性能条目
   */
  private handleNavigationEntry(entry: PerformanceNavigationTiming): void {
    const metrics: Partial<PerformanceMetrics> = {
      pageLoadTime: entry.loadEventEnd - entry.navigationStart
    }
    this.addMetrics(metrics)
  }

  /**
   * 处理绘制性能条目
   */
  private handlePaintEntry(entry: PerformancePaintTiming): void {
    if (entry.name === 'first-contentful-paint') {
      const metrics: Partial<PerformanceMetrics> = {
        firstContentfulPaint: entry.startTime
      }
      this.addMetrics(metrics)
    }
  }

  /**
   * 处理最大内容绘制条目
   */
  private handleLCPEntry(entry: any): void {
    const metrics: Partial<PerformanceMetrics> = {
      largestContentfulPaint: entry.startTime
    }
    this.addMetrics(metrics)
  }

  /**
   * 处理首次输入延迟条目
   */
  private handleFIDEntry(entry: any): void {
    const metrics: Partial<PerformanceMetrics> = {
      firstInputDelay: entry.processingStart - entry.startTime
    }
    this.addMetrics(metrics)
  }

  /**
   * 开始内存监控
   */
  private startMemoryMonitoring(): void {
    if (!(performance as any).memory) return

    const monitorMemory = () => {
      const memory = (performance as any).memory
      const metrics: Partial<PerformanceMetrics> = {
        memoryUsage: {
          used: memory.usedJSHeapSize,
          total: memory.totalJSHeapSize,
          limit: memory.jsHeapSizeLimit
        }
      }
      this.addMetrics(metrics)
    }

    // 立即执行一次
    monitorMemory()

    // 定期监控
    this.memoryTimer = window.setInterval(monitorMemory, this.config.reportInterval)
  }

  /**
   * 开始网络监控
   */
  private startNetworkMonitoring(): void {
    if (!(navigator as any).connection) return

    const monitorNetwork = () => {
      const connection = (navigator as any).connection
      const metrics: Partial<PerformanceMetrics> = {
        networkInfo: {
          effectiveType: connection.effectiveType,
          downlink: connection.downlink,
          rtt: connection.rtt
        }
      }
      this.addMetrics(metrics)
    }

    // 立即执行一次
    monitorNetwork()

    // 监听网络变化
    connection.addEventListener('change', monitorNetwork)

    // 定期监控
    this.networkTimer = window.setInterval(monitorNetwork, this.config.reportInterval)
  }

  /**
   * 收集初始指标
   */
  private async collectInitialMetrics(): Promise<void> {
    // 等待页面加载完成
    if (document.readyState !== 'complete') {
      await new Promise(resolve => {
        window.addEventListener('load', resolve, { once: true })
      })
    }

    // 收集导航时间
    const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming
    if (navigation) {
      this.handleNavigationEntry(navigation)
    }

    // 收集绘制时间
    const paintEntries = performance.getEntriesByType('paint') as PerformancePaintTiming[]
    paintEntries.forEach(entry => this.handlePaintEntry(entry))
  }

  /**
   * 添加性能指标
   */
  private addMetrics(partialMetrics: Partial<PerformanceMetrics>): void {
    // 应用采样率
    if (Math.random() > this.config.sampleRate) {
      return
    }

    const timestamp = Date.now()
    const existingMetrics = this.metrics[this.metrics.length - 1] || {}
    
    const metrics: PerformanceMetrics = {
      pageLoadTime: 0,
      firstContentfulPaint: 0,
      largestContentfulPaint: 0,
      firstInputDelay: 0,
      cumulativeLayoutShift: 0,
      ...existingMetrics,
      ...partialMetrics
    }

    this.metrics.push(metrics)

    // 限制缓存大小
    if (this.metrics.length > this.config.maxCacheSize) {
      this.metrics = this.metrics.slice(-this.config.maxCacheSize)
    }

    // 触发性能事件
    this.dispatchPerformanceEvent(metrics)
  }

  /**
   * 触发性能事件
   */
  private dispatchPerformanceEvent(metrics: PerformanceMetrics): void {
    const event = new CustomEvent('app:performance', {
      detail: { metrics }
    })
    window.dispatchEvent(event)
  }

  /**
   * 获取性能指标
   */
  getMetrics(): ServiceResponse<PerformanceMetrics[]> {
    try {
      return this.success([...this.metrics])
    } catch (error) {
      return this.handleError(error, 'GET_METRICS_ERROR')
    }
  }

  /**
   * 获取最新指标
   */
  getLatestMetrics(): ServiceResponse<PerformanceMetrics | null> {
    try {
      const latest = this.metrics[this.metrics.length - 1] || null
      return this.success(latest)
    } catch (error) {
      return this.handleError(error, 'GET_LATEST_METRICS_ERROR')
    }
  }

  /**
   * 清除指标
   */
  clearMetrics(): ServiceResponse<void> {
    try {
      this.metrics = []
      return this.success(undefined)
    } catch (error) {
      return this.handleError(error, 'CLEAR_METRICS_ERROR')
    }
  }

  /**
   * 测量函数执行时间
   */
  measureFunction<T>(name: string, fn: () => T): T {
    const start = performance.now()
    const result = fn()
    const end = performance.now()
    
    console.log(`${name} took ${end - start} milliseconds`)
    
    return result
  }

  /**
   * 测量异步函数执行时间
   */
  async measureAsyncFunction<T>(name: string, fn: () => Promise<T>): Promise<T> {
    const start = performance.now()
    const result = await fn()
    const end = performance.now()
    
    console.log(`${name} took ${end - start} milliseconds`)
    
    return result
  }

  /**
   * 销毁服务
   */
  destroy(): void {
    if (this.observer) {
      this.observer.disconnect()
      this.observer = undefined
    }

    if (this.memoryTimer) {
      clearInterval(this.memoryTimer)
      this.memoryTimer = undefined
    }

    if (this.networkTimer) {
      clearInterval(this.networkTimer)
      this.networkTimer = undefined
    }

    this.metrics = []
  }
}

// 导出单例实例
export const performanceService = PerformanceService.getInstance()
