/**
 * 格式化工具函数
 * Formatting utility functions
 */

// 不再需要导入CurrencyCode类型，因为已固定使用人民币

// ==========================================================================
// 货币格式化 - Currency Formatting
// ==========================================================================

/** 人民币格式化配置 */
const CNY_CONFIG = {
  symbol: '¥',
  locale: 'zh-CN',
  minimumFractionDigits: 2,
  maximumFractionDigits: 2
}

/**
 * 格式化货币金额 - 固定使用人民币
 * @param amount 金额（分为单位）
 * @param showSymbol 是否显示货币符号
 * @returns 格式化后的货币字符串
 */
export function formatCurrency(
  amount: number,
  showSymbol: boolean = true
): string {
  // 将分转换为元
  const yuan = Math.round(amount) / 100

  const formatter = new Intl.NumberFormat(CNY_CONFIG.locale, {
    style: showSymbol ? 'currency' : 'decimal',
    currency: showSymbol ? 'CNY' : undefined,
    minimumFractionDigits: CNY_CONFIG.minimumFractionDigits,
    maximumFractionDigits: CNY_CONFIG.maximumFractionDigits
  })

  return formatter.format(yuan)
}

/**
 * 格式化简化的货币金额（用于显示大数值）- 固定使用人民币
 * @param amount 金额（分为单位）
 * @returns 格式化后的简化货币字符串
 */
export function formatCurrencyCompact(amount: number): string {
  const yuan = Math.round(amount) / 100

  if (Math.abs(yuan) >= 10000) {
    const wan = yuan / 10000
    return `${CNY_CONFIG.symbol}${wan.toFixed(1)}万`
  } else if (Math.abs(yuan) >= 1000) {
    const k = yuan / 1000
    return `${CNY_CONFIG.symbol}${k.toFixed(1)}k`
  }

  return formatCurrency(amount)
}

/**
 * 解析货币字符串为分
 * @param currencyString 货币字符串
 * @returns 金额（分为单位）
 */
export function parseCurrency(currencyString: string): number {
  // 移除所有非数字和小数点的字符
  const cleanString = currencyString.replace(/[^\d.-]/g, '')
  const yuan = parseFloat(cleanString) || 0
  return Math.round(yuan * 100)
}

// ==========================================================================
// 日期格式化 - Date Formatting
// ==========================================================================

/**
 * 格式化日期
 * @param date 日期字符串或Date对象
 * @param format 格式类型
 * @returns 格式化后的日期字符串
 */
export function formatDate(
  date: string | Date,
  format: 'full' | 'short' | 'month' | 'year' | 'time' | 'datetime' = 'short'
): string {
  const dateObj = typeof date === 'string' ? new Date(date) : date

  if (isNaN(dateObj.getTime())) {
    return '无效日期'
  }

  const year = dateObj.getFullYear()
  const month = dateObj.getMonth() + 1
  const day = dateObj.getDate()
  const hours = dateObj.getHours()
  const minutes = dateObj.getMinutes()
  const seconds = dateObj.getSeconds()

  switch (format) {
    case 'full':
      return `${year}年${month}月${day}日`
    case 'short':
      return `${month}月${day}日`
    case 'month':
      return `${year}年${month}月`
    case 'year':
      return `${year}年`
    case 'time':
      return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`
    case 'datetime':
      return `${year}年${month}月${day}日 ${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`
    default:
      return dateObj.toLocaleDateString('zh-CN')
  }
}

/**
 * 格式化相对时间
 * @param date 日期字符串或Date对象
 * @returns 相对时间字符串
 */
export function formatRelativeTime(date: string | Date): string {
  const dateObj = typeof date === 'string' ? new Date(date) : date
  const now = new Date()
  const diffMs = now.getTime() - dateObj.getTime()
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))

  if (diffDays === 0) {
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60))
    if (diffHours === 0) {
      const diffMinutes = Math.floor(diffMs / (1000 * 60))
      if (diffMinutes === 0) {
        return '刚刚'
      }
      return `${diffMinutes}分钟前`
    }
    return `${diffHours}小时前`
  } else if (diffDays === 1) {
    return '昨天'
  } else if (diffDays === 2) {
    return '前天'
  } else if (diffDays < 7) {
    return `${diffDays}天前`
  } else if (diffDays < 30) {
    const weeks = Math.floor(diffDays / 7)
    return `${weeks}周前`
  } else if (diffDays < 365) {
    const months = Math.floor(diffDays / 30)
    return `${months}个月前`
  } else {
    const years = Math.floor(diffDays / 365)
    return `${years}年前`
  }
}

/**
 * 获取日期范围描述
 * @param startDate 开始日期
 * @param endDate 结束日期
 * @returns 日期范围描述
 */
export function formatDateRange(startDate: string, endDate: string): string {
  const start = new Date(startDate)
  const end = new Date(endDate)

  if (startDate === endDate) {
    return formatDate(start, 'full')
  }

  if (start.getFullYear() === end.getFullYear()) {
    if (start.getMonth() === end.getMonth()) {
      return `${start.getFullYear()}年${start.getMonth() + 1}月${start.getDate()}-${end.getDate()}日`
    }
    return `${start.getFullYear()}年${start.getMonth() + 1}月${start.getDate()}日 - ${end.getMonth() + 1}月${end.getDate()}日`
  }

  return `${formatDate(start, 'full')} - ${formatDate(end, 'full')}`
}

// ==========================================================================
// 数字格式化 - Number Formatting
// ==========================================================================

/**
 * 格式化数字（添加千分位分隔符）
 * @param num 数字
 * @param decimals 小数位数
 * @returns 格式化后的数字字符串
 */
export function formatNumber(num: number, decimals: number = 0, locale: string = 'en-US'): string {
  return new Intl.NumberFormat(locale, {
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals
  }).format(num)
}

/**
 * 格式化百分比
 * @param value 数值（0-1之间）
 * @param decimals 小数位数
 * @returns 格式化后的百分比字符串
 */
export function formatPercentage(value: number, decimals: number = 1, locale: string = 'en-US'): string {
  return new Intl.NumberFormat(locale, {
    style: 'percent',
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals
  }).format(value)
}

/**
 * 格式化文件大小
 * @param bytes 字节数
 * @returns 格式化后的文件大小字符串
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 B'

  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))

  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`
}

// ==========================================================================
// 文本格式化 - Text Formatting
// ==========================================================================

/**
 * 截断文本
 * @param text 原始文本
 * @param maxLength 最大长度
 * @param suffix 后缀
 * @returns 截断后的文本
 */
export function truncateText(text: string, maxLength: number, suffix: string = '...'): string {
  if (text.length <= maxLength) {
    return text
  }
  return text.substring(0, maxLength - suffix.length) + suffix
}

/**
 * 首字母大写
 * @param text 文本
 * @returns 首字母大写的文本
 */
export function capitalize(text: string): string {
  if (!text) return text
  return text.charAt(0).toUpperCase() + text.slice(1).toLowerCase()
}

/**
 * 驼峰命名转换
 * @param text 文本
 * @returns 驼峰命名的文本
 */
export function toCamelCase(text: string): string {
  return text
    .replace(/(?:^\w|[A-Z]|\b\w)/g, (word, index) => {
      return index === 0 ? word.toLowerCase() : word.toUpperCase()
    })
    .replace(/\s+/g, '')
}

/**
 * 短横线命名转换
 * @param text 文本
 * @returns 短横线命名的文本
 */
export function toKebabCase(text: string): string {
  return text
    .replace(/([a-z])([A-Z])/g, '$1-$2')
    .replace(/\s+/g, '-')
    .toLowerCase()
}

/**
 * 隐藏敏感信息
 * @param text 原始文本
 * @param visibleStart 开始显示的字符数
 * @param visibleEnd 结束显示的字符数
 * @param maskChar 遮罩字符
 * @returns 遮罩后的文本
 */
export function maskSensitiveInfo(
  text: string,
  visibleStart: number = 3,
  visibleEnd: number = 3,
  maskChar: string = '*'
): string {
  if (text.length <= visibleStart + visibleEnd) {
    return text
  }

  const start = text.substring(0, visibleStart)
  const end = text.substring(text.length - visibleEnd)
  const maskLength = text.length - visibleStart - visibleEnd
  const mask = maskChar.repeat(maskLength)

  return start + mask + end
}

// ==========================================================================
// 颜色格式化 - Color Formatting
// ==========================================================================

/**
 * 十六进制颜色转RGB
 * @param hex 十六进制颜色值
 * @returns RGB对象
 */
export function hexToRgb(hex: string): { r: number; g: number; b: number } | null {
  const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex)
  return result ? {
    r: parseInt(result[1], 16),
    g: parseInt(result[2], 16),
    b: parseInt(result[3], 16)
  } : null
}

/**
 * RGB转十六进制颜色
 * @param r 红色值
 * @param g 绿色值
 * @param b 蓝色值
 * @returns 十六进制颜色值
 */
export function rgbToHex(r: number, g: number, b: number): string {
  return `#${((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1)}`
}

/**
 * 获取颜色的亮度
 * @param hex 十六进制颜色值
 * @returns 亮度值（0-255）
 */
export function getColorBrightness(hex: string): number {
  const rgb = hexToRgb(hex)
  if (!rgb) return 0
  return Math.round((rgb.r * 299 + rgb.g * 587 + rgb.b * 114) / 1000)
}

/**
 * 判断颜色是否为深色
 * @param hex 十六进制颜色值
 * @returns 是否为深色
 */
export function isDarkColor(hex: string): boolean {
  return getColorBrightness(hex) < 128
}
