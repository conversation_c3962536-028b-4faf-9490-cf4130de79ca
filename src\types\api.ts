/**
 * API 相关类型定义
 * API related type definitions
 */

// ==========================================================================
// HTTP 相关类型 - HTTP Related Types
// ==========================================================================

/** HTTP 方法 */
export type HttpMethod = 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH' | 'HEAD' | 'OPTIONS'

/** HTTP 状态码 */
export type HttpStatusCode = 
  | 200 | 201 | 202 | 204
  | 400 | 401 | 403 | 404 | 409 | 422 | 429
  | 500 | 502 | 503 | 504

/** 请求头 */
export interface RequestHeaders {
  [key: string]: string
}

/** 响应头 */
export interface ResponseHeaders {
  [key: string]: string
}

/** HTTP 请求配置 */
export interface RequestConfig {
  /** 请求URL */
  url: string
  /** 请求方法 */
  method: HttpMethod
  /** 请求头 */
  headers?: RequestHeaders
  /** 请求参数 */
  params?: Record<string, any>
  /** 请求体 */
  data?: any
  /** 超时时间（毫秒） */
  timeout?: number
  /** 是否携带凭证 */
  withCredentials?: boolean
  /** 响应类型 */
  responseType?: 'json' | 'text' | 'blob' | 'arraybuffer'
  /** 请求拦截器 */
  onRequest?: (config: RequestConfig) => RequestConfig | Promise<RequestConfig>
  /** 响应拦截器 */
  onResponse?: (response: ApiResponse) => ApiResponse | Promise<ApiResponse>
  /** 错误拦截器 */
  onError?: (error: ApiError) => void | Promise<void>
}

/** HTTP 响应 */
export interface HttpResponse<T = any> {
  /** 响应数据 */
  data: T
  /** 状态码 */
  status: HttpStatusCode
  /** 状态文本 */
  statusText: string
  /** 响应头 */
  headers: ResponseHeaders
  /** 请求配置 */
  config: RequestConfig
}

// ==========================================================================
// API 响应类型 - API Response Types
// ==========================================================================

/** 基础 API 响应 */
export interface BaseApiResponse {
  /** 是否成功 */
  success: boolean
  /** 响应消息 */
  message?: string
  /** 错误代码 */
  code?: string
  /** 时间戳 */
  timestamp: number
}

/** 成功响应 */
export interface SuccessResponse<T = any> extends BaseApiResponse {
  success: true
  /** 响应数据 */
  data: T
}

/** 错误响应 */
export interface ErrorResponse extends BaseApiResponse {
  success: false
  /** 错误详情 */
  error: {
    /** 错误代码 */
    code: string
    /** 错误消息 */
    message: string
    /** 错误详情 */
    details?: any
    /** 错误堆栈 */
    stack?: string
  }
}

/** API 响应联合类型 */
export type ApiResponse<T = any> = SuccessResponse<T> | ErrorResponse

/** 分页响应 */
export interface PaginatedResponse<T> extends SuccessResponse<T[]> {
  /** 分页信息 */
  pagination: {
    /** 当前页 */
    page: number
    /** 每页大小 */
    pageSize: number
    /** 总数 */
    total: number
    /** 总页数 */
    totalPages: number
    /** 是否有下一页 */
    hasNext: boolean
    /** 是否有上一页 */
    hasPrev: boolean
  }
}

// ==========================================================================
// API 错误类型 - API Error Types
// ==========================================================================

/** API 错误 */
export interface ApiError extends Error {
  /** 错误代码 */
  code: string
  /** HTTP 状态码 */
  status?: HttpStatusCode
  /** 响应数据 */
  response?: HttpResponse
  /** 请求配置 */
  config?: RequestConfig
  /** 是否为网络错误 */
  isNetworkError: boolean
  /** 是否为超时错误 */
  isTimeoutError: boolean
  /** 是否为取消错误 */
  isCancelError: boolean
}

/** 验证错误 */
export interface ValidationApiError extends ApiError {
  /** 字段错误 */
  fieldErrors: Record<string, string[]>
}

/** 业务错误 */
export interface BusinessApiError extends ApiError {
  /** 业务错误代码 */
  businessCode: string
  /** 业务错误数据 */
  businessData?: any
}

// ==========================================================================
// API 客户端类型 - API Client Types
// ==========================================================================

/** API 客户端配置 */
export interface ApiClientConfig {
  /** 基础URL */
  baseURL: string
  /** 默认超时时间 */
  timeout: number
  /** 默认请求头 */
  headers: RequestHeaders
  /** 重试配置 */
  retry: {
    /** 重试次数 */
    times: number
    /** 重试延迟 */
    delay: number
    /** 重试条件 */
    condition: (error: ApiError) => boolean
  }
  /** 缓存配置 */
  cache: {
    /** 是否启用缓存 */
    enabled: boolean
    /** 默认缓存时间 */
    ttl: number
    /** 缓存键生成函数 */
    keyGenerator: (config: RequestConfig) => string
  }
}

/** API 客户端接口 */
export interface ApiClient {
  /** GET 请求 */
  get<T = any>(url: string, config?: Partial<RequestConfig>): Promise<T>
  /** POST 请求 */
  post<T = any>(url: string, data?: any, config?: Partial<RequestConfig>): Promise<T>
  /** PUT 请求 */
  put<T = any>(url: string, data?: any, config?: Partial<RequestConfig>): Promise<T>
  /** DELETE 请求 */
  delete<T = any>(url: string, config?: Partial<RequestConfig>): Promise<T>
  /** PATCH 请求 */
  patch<T = any>(url: string, data?: any, config?: Partial<RequestConfig>): Promise<T>
  /** 通用请求 */
  request<T = any>(config: RequestConfig): Promise<T>
}

// ==========================================================================
// 请求拦截器类型 - Request Interceptor Types
// ==========================================================================

/** 请求拦截器 */
export interface RequestInterceptor {
  /** 拦截器ID */
  id: string
  /** 请求拦截函数 */
  onRequest?: (config: RequestConfig) => RequestConfig | Promise<RequestConfig>
  /** 请求错误拦截函数 */
  onRequestError?: (error: any) => any
}

/** 响应拦截器 */
export interface ResponseInterceptor {
  /** 拦截器ID */
  id: string
  /** 响应拦截函数 */
  onResponse?: (response: HttpResponse) => HttpResponse | Promise<HttpResponse>
  /** 响应错误拦截函数 */
  onResponseError?: (error: ApiError) => any
}

// ==========================================================================
// 上传下载类型 - Upload/Download Types
// ==========================================================================

/** 文件上传配置 */
export interface UploadConfig {
  /** 上传URL */
  url: string
  /** 文件字段名 */
  fileField: string
  /** 额外数据 */
  data?: Record<string, any>
  /** 请求头 */
  headers?: RequestHeaders
  /** 上传进度回调 */
  onProgress?: (progress: UploadProgress) => void
  /** 上传成功回调 */
  onSuccess?: (response: any) => void
  /** 上传失败回调 */
  onError?: (error: ApiError) => void
}

/** 上传进度 */
export interface UploadProgress {
  /** 已上传字节数 */
  loaded: number
  /** 总字节数 */
  total: number
  /** 上传百分比 */
  percentage: number
  /** 上传速度（字节/秒） */
  speed: number
  /** 剩余时间（秒） */
  timeRemaining: number
}

/** 下载配置 */
export interface DownloadConfig {
  /** 下载URL */
  url: string
  /** 文件名 */
  filename?: string
  /** 请求头 */
  headers?: RequestHeaders
  /** 下载进度回调 */
  onProgress?: (progress: DownloadProgress) => void
  /** 下载成功回调 */
  onSuccess?: (blob: Blob) => void
  /** 下载失败回调 */
  onError?: (error: ApiError) => void
}

/** 下载进度 */
export interface DownloadProgress {
  /** 已下载字节数 */
  loaded: number
  /** 总字节数 */
  total: number
  /** 下载百分比 */
  percentage: number
  /** 下载速度（字节/秒） */
  speed: number
  /** 剩余时间（秒） */
  timeRemaining: number
}

// ==========================================================================
// WebSocket 类型 - WebSocket Types
// ==========================================================================

/** WebSocket 连接状态 */
export type WebSocketState = 'connecting' | 'connected' | 'disconnecting' | 'disconnected' | 'error'

/** WebSocket 消息类型 */
export interface WebSocketMessage<T = any> {
  /** 消息类型 */
  type: string
  /** 消息数据 */
  data: T
  /** 消息ID */
  id?: string
  /** 时间戳 */
  timestamp: number
}

/** WebSocket 配置 */
export interface WebSocketConfig {
  /** WebSocket URL */
  url: string
  /** 协议 */
  protocols?: string | string[]
  /** 重连配置 */
  reconnect: {
    /** 是否自动重连 */
    enabled: boolean
    /** 重连间隔 */
    interval: number
    /** 最大重连次数 */
    maxAttempts: number
  }
  /** 心跳配置 */
  heartbeat: {
    /** 是否启用心跳 */
    enabled: boolean
    /** 心跳间隔 */
    interval: number
    /** 心跳消息 */
    message: string
  }
}

/** WebSocket 客户端接口 */
export interface WebSocketClient {
  /** 连接状态 */
  state: WebSocketState
  /** 连接 */
  connect(): Promise<void>
  /** 断开连接 */
  disconnect(): void
  /** 发送消息 */
  send<T = any>(message: WebSocketMessage<T>): void
  /** 监听消息 */
  on<T = any>(type: string, handler: (data: T) => void): void
  /** 取消监听 */
  off(type: string, handler?: Function): void
}
