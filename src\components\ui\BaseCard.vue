<template>
  <div :class="cardClasses">
    <!-- 头部 -->
    <div v-if="$slots.header || title" class="p-6 pb-4">
      <slot name="header">
        <h3 v-if="title" class="text-lg font-semibold text-gray-900 dark:text-gray-100">
          {{ title }}
        </h3>
      </slot>
    </div>
    
    <!-- 内容 -->
    <div :class="bodyClasses">
      <slot />
    </div>
    
    <!-- 底部 -->
    <div v-if="$slots.footer" class="p-6 pt-4 border-t border-gray-200 dark:border-gray-700">
      <slot name="footer" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  title?: string
  variant?: 'default' | 'elevated' | 'interactive'
  padding?: 'none' | 'sm' | 'md' | 'lg'
  hover?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  variant: 'default',
  padding: 'md',
  hover: false
})

const cardClasses = computed(() => {
  const baseClasses = ['card']
  
  // Variant classes
  switch (props.variant) {
    case 'elevated':
      baseClasses.push('card-elevated')
      break
    case 'interactive':
      baseClasses.push('card-interactive')
      break
  }
  
  // Hover effect
  if (props.hover) {
    baseClasses.push('hover:bg-gray-50 dark:hover:bg-gray-900 transition-colors duration-200 cursor-pointer')
  }
  
  return baseClasses
})

const bodyClasses = computed(() => {
  const classes = []
  
  // Padding classes
  switch (props.padding) {
    case 'none':
      // No padding
      break
    case 'sm':
      classes.push('p-4')
      break
    case 'lg':
      classes.push('p-8')
      break
    default:
      classes.push('p-6')
  }
  
  // Adjust padding if header exists
  if (props.$slots?.header || props.title) {
    classes.push('pt-0')
  }
  
  // Adjust padding if footer exists
  if (props.$slots?.footer) {
    classes.push('pb-0')
  }
  
  return classes
})
</script>
