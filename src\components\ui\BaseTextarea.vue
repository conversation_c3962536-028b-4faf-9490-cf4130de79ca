<template>
  <div class="textarea-wrapper" :class="wrapperClasses">
    <!-- 标签 -->
    <label 
      v-if="label" 
      :for="textareaId" 
      class="textarea-label"
      :class="labelClasses"
    >
      {{ label }}
      <span v-if="required" class="textarea-required">*</span>
    </label>
    
    <!-- 文本域容器 -->
    <div class="textarea-container" :class="containerClasses">
      <textarea
        :id="textareaId"
        ref="textareaRef"
        :value="modelValue"
        :placeholder="placeholder"
        :disabled="disabled"
        :readonly="readonly"
        :required="required"
        :rows="rows"
        :cols="cols"
        :maxlength="maxLength"
        :minlength="minLength"
        :autocomplete="autocomplete"
        :aria-label="ariaLabel"
        :aria-describedby="ariaDescribedby"
        :class="textareaClasses"
        @input="handleInput"
        @blur="handleBlur"
        @focus="handleFocus"
        @keydown="handleKeydown"
        @keyup="handleKeyup"
        @change="handleChange"
      ></textarea>
      
      <!-- 调整大小手柄 -->
      <div 
        v-if="resizable && !disabled && !readonly" 
        class="textarea-resize-handle"
        @mousedown="startResize"
      >
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4" />
        </svg>
      </div>
    </div>
    
    <!-- 底部信息栏 -->
    <div v-if="showFooter" class="textarea-footer">
      <!-- 错误信息 -->
      <div 
        v-if="error" 
        class="textarea-error-message"
        :id="`${textareaId}-error`"
      >
        {{ error }}
      </div>
      
      <!-- 帮助文本 */
      <div 
        v-else-if="helpText" 
        class="textarea-help-text"
        :id="`${textareaId}-help`"
      >
        {{ helpText }}
      </div>
      
      <!-- 字符计数 -->
      <div 
        v-if="showCount && maxLength" 
        class="textarea-count"
        :class="countClasses"
      >
        {{ currentLength }}/{{ maxLength }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, nextTick, onMounted, onUnmounted } from 'vue'
import type { TextareaProps } from '@/types/components'

interface Props extends /* @vue-ignore */ TextareaProps {
  /** 帮助文本 */
  helpText?: string
  /** 是否显示字符计数 */
  showCount?: boolean
  /** 是否可调整大小 */
  resizable?: boolean
  /** 自动完成 */
  autocomplete?: string
  /** 无障碍标签 */
  ariaLabel?: string
  /** 无障碍描述 */
  ariaDescribedby?: string
}

const props = withDefaults(defineProps<Props>(), {
  size: 'medium',
  disabled: false,
  readonly: false,
  required: false,
  rows: 4,
  showCount: false,
  resizable: true,
  autoResize: false
})

const emit = defineEmits<{
  'update:modelValue': [value: string]
  input: [value: string]
  change: [value: string]
  focus: [event: FocusEvent]
  blur: [event: FocusEvent]
  keydown: [event: KeyboardEvent]
  keyup: [event: KeyboardEvent]
  resize: [height: number]
}>()

// 响应式数据
const textareaRef = ref<HTMLTextAreaElement>()
const textareaId = ref(`textarea-${Math.random().toString(36).substr(2, 9)}`)
const isResizing = ref(false)
const startY = ref(0)
const startHeight = ref(0)

// 计算属性
const currentLength = computed(() => {
  return String(props.modelValue || '').length
})

const showFooter = computed(() => {
  return props.error || props.helpText || (props.showCount && props.maxLength)
})

const countClasses = computed(() => [
  'textarea-count',
  {
    'textarea-count-warning': props.maxLength && currentLength.value > props.maxLength * 0.8,
    'textarea-count-error': props.maxLength && currentLength.value > props.maxLength
  }
])

const wrapperClasses = computed(() => [
  'textarea-wrapper',
  {
    'textarea-wrapper-error': props.error,
    'textarea-wrapper-disabled': props.disabled,
    'textarea-wrapper-readonly': props.readonly
  }
])

const labelClasses = computed(() => [
  'textarea-label',
  `textarea-label-${props.size}`,
  {
    'textarea-label-required': props.required,
    'textarea-label-disabled': props.disabled
  }
])

const containerClasses = computed(() => [
  'textarea-container',
  `textarea-container-${props.size}`,
  {
    'textarea-container-error': props.error,
    'textarea-container-disabled': props.disabled,
    'textarea-container-readonly': props.readonly,
    'textarea-container-resizable': props.resizable && !props.disabled && !props.readonly
  }
])

const textareaClasses = computed(() => [
  'textarea',
  `textarea-${props.size}`,
  {
    'textarea-error': props.error,
    'textarea-disabled': props.disabled,
    'textarea-readonly': props.readonly,
    'textarea-auto-resize': props.autoResize
  }
])

// 方法
const focus = () => {
  nextTick(() => {
    textareaRef.value?.focus()
  })
}

const blur = () => {
  textareaRef.value?.blur()
}

const select = () => {
  textareaRef.value?.select()
}

const autoResize = () => {
  if (!props.autoResize || !textareaRef.value) return
  
  const textarea = textareaRef.value
  textarea.style.height = 'auto'
  const newHeight = Math.max(textarea.scrollHeight, textarea.rows * 24)
  textarea.style.height = `${newHeight}px`
  
  emit('resize', newHeight)
}

const startResize = (event: MouseEvent) => {
  if (!textareaRef.value) return
  
  isResizing.value = true
  startY.value = event.clientY
  startHeight.value = textareaRef.value.offsetHeight
  
  document.addEventListener('mousemove', handleResize)
  document.addEventListener('mouseup', stopResize)
  
  event.preventDefault()
}

const handleResize = (event: MouseEvent) => {
  if (!isResizing.value || !textareaRef.value) return
  
  const deltaY = event.clientY - startY.value
  const newHeight = Math.max(startHeight.value + deltaY, 60) // 最小高度 60px
  
  textareaRef.value.style.height = `${newHeight}px`
  emit('resize', newHeight)
}

const stopResize = () => {
  isResizing.value = false
  document.removeEventListener('mousemove', handleResize)
  document.removeEventListener('mouseup', stopResize)
}

// 事件处理
const handleInput = (event: Event) => {
  const target = event.target as HTMLTextAreaElement
  const value = target.value
  
  emit('update:modelValue', value)
  emit('input', value)
  
  if (props.autoResize) {
    autoResize()
  }
}

const handleChange = (event: Event) => {
  const target = event.target as HTMLTextAreaElement
  const value = target.value
  
  emit('change', value)
}

const handleFocus = (event: FocusEvent) => {
  emit('focus', event)
}

const handleBlur = (event: FocusEvent) => {
  emit('blur', event)
}

const handleKeydown = (event: KeyboardEvent) => {
  emit('keydown', event)
}

const handleKeyup = (event: KeyboardEvent) => {
  emit('keyup', event)
}

// 生命周期
onMounted(() => {
  if (props.autoResize) {
    autoResize()
  }
})

onUnmounted(() => {
  document.removeEventListener('mousemove', handleResize)
  document.removeEventListener('mouseup', stopResize)
})

// 暴露方法
defineExpose({
  focus,
  blur,
  select,
  textareaRef
})
</script>

<style scoped>
/* 文本域包装器 */
.textarea-wrapper {
  @apply space-y-1;
}

.textarea-wrapper-error {
  /* 错误状态样式 */
}

.textarea-wrapper-disabled {
  @apply opacity-60;
}

.textarea-wrapper-readonly {
  /* 只读状态样式 */
}

/* 标签样式 */
.textarea-label {
  @apply block font-medium text-gray-700 dark:text-gray-300;
}

.textarea-label-small {
  @apply text-xs;
}

.textarea-label-medium {
  @apply text-sm;
}

.textarea-label-large {
  @apply text-base;
}

.textarea-label-required {
  /* 必填标签样式 */
}

.textarea-label-disabled {
  @apply text-gray-400 dark:text-gray-600;
}

.textarea-required {
  @apply text-red-500 ml-1;
}

/* 文本域容器 */
.textarea-container {
  @apply relative;
}

.textarea-container-resizable {
  /* 可调整大小容器样式 */
}

/* 文本域样式 */
.textarea {
  @apply w-full border border-gray-300 rounded-lg bg-white text-gray-900;
  @apply placeholder-gray-400 transition-all duration-200 resize-none;
  @apply focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent;
  @apply dark:bg-gray-800 dark:border-gray-600 dark:text-gray-100;
  @apply dark:placeholder-gray-500 dark:focus:ring-blue-400;
}

.textarea-small {
  @apply px-3 py-1.5 text-sm;
}

.textarea-medium {
  @apply px-4 py-2 text-base;
}

.textarea-large {
  @apply px-4 py-3 text-lg;
}

.textarea-error {
  @apply border-red-300 focus:ring-red-500 focus:border-red-500;
  @apply dark:border-red-600 dark:focus:ring-red-400;
}

.textarea-disabled {
  @apply bg-gray-50 cursor-not-allowed;
  @apply dark:bg-gray-900;
}

.textarea-readonly {
  @apply bg-gray-50 cursor-default;
  @apply dark:bg-gray-900;
}

.textarea-auto-resize {
  @apply overflow-hidden;
}

/* 调整大小手柄 */
.textarea-resize-handle {
  @apply absolute bottom-0 right-0 w-4 h-4 cursor-se-resize;
  @apply text-gray-400 hover:text-gray-600 transition-colors;
  @apply dark:text-gray-500 dark:hover:text-gray-300;
}

/* 底部信息栏 */
.textarea-footer {
  @apply flex justify-between items-center;
}

/* 错误信息 */
.textarea-error-message {
  @apply text-sm text-red-600 dark:text-red-400 flex-1;
}

/* 帮助文本 */
.textarea-help-text {
  @apply text-sm text-gray-500 dark:text-gray-400 flex-1;
}

/* 字符计数 */
.textarea-count {
  @apply text-xs text-gray-400 ml-2;
  @apply dark:text-gray-500;
}

.textarea-count-warning {
  @apply text-yellow-600 dark:text-yellow-400;
}

.textarea-count-error {
  @apply text-red-600 dark:text-red-400;
}
</style>
