import type { ServiceResponse, ServiceError } from '@/types'

/**
 * 服务基类
 * 提供通用的错误处理和响应格式化功能
 */
export abstract class BaseService {
  /**
   * 创建成功响应
   */
  protected createSuccessResponse<T>(data: T): ServiceResponse<T> {
    return {
      success: true,
      data
    }
  }

  /**
   * 创建错误响应
   */
  protected createErrorResponse(error: ServiceError): ServiceResponse {
    return {
      success: false,
      error
    }
  }

  /**
   * 创建服务错误
   */
  protected createError(
    code: string,
    message: string,
    details?: any
  ): ServiceError {
    const error: ServiceError = {
      code,
      message,
      details
    }

    // 开发环境下添加堆栈信息
    if (import.meta.env.DEV) {
      error.stack = new Error().stack
    }

    return error
  }

  /**
   * 安全执行异步操作
   */
  protected async safeExecute<T>(
    operation: () => Promise<T>,
    errorCode: string = 'OPERATION_FAILED'
  ): Promise<ServiceResponse<T>> {
    try {
      const result = await operation()
      return this.createSuccessResponse(result)
    } catch (error) {
      console.error(`Service error [${errorCode}]:`, error)
      
      const serviceError = this.createError(
        errorCode,
        error instanceof Error ? error.message : 'Unknown error occurred',
        error
      )
      
      return this.createErrorResponse(serviceError)
    }
  }

  /**
   * 同步安全执行
   */
  protected safeExecuteSync<T>(
    operation: () => T,
    errorCode: string = 'OPERATION_FAILED'
  ): ServiceResponse<T> {
    try {
      const result = operation()
      return this.createSuccessResponse(result)
    } catch (error) {
      console.error(`Service error [${errorCode}]:`, error)
      
      const serviceError = this.createError(
        errorCode,
        error instanceof Error ? error.message : 'Unknown error occurred',
        error
      )
      
      return this.createErrorResponse(serviceError)
    }
  }

  /**
   * 验证必需参数
   */
  protected validateRequired(params: Record<string, any>): ServiceError | null {
    for (const [key, value] of Object.entries(params)) {
      if (value === null || value === undefined || value === '') {
        return this.createError(
          'VALIDATION_ERROR',
          `Required parameter '${key}' is missing or empty`
        )
      }
    }
    return null
  }

  /**
   * 记录操作日志
   */
  protected log(level: 'info' | 'warn' | 'error', message: string, data?: any): void {
    const timestamp = new Date().toISOString()
    const logMessage = `[${timestamp}] [${this.constructor.name}] ${message}`
    
    switch (level) {
      case 'info':
        console.info(logMessage, data)
        break
      case 'warn':
        console.warn(logMessage, data)
        break
      case 'error':
        console.error(logMessage, data)
        break
    }
  }
}
