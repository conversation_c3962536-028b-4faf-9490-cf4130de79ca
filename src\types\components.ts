/**
 * 组件相关类型定义
 * Component related type definitions
 */

import type { Component, VNode, Ref } from 'vue'

// ==========================================================================
// 基础组件类型 - Base Component Types
// ==========================================================================

/** 组件大小 */
export type ComponentSize = 'small' | 'medium' | 'large'

/** 组件变体 */
export type ComponentVariant = 'primary' | 'secondary' | 'success' | 'warning' | 'error' | 'info'

/** 组件状态 */
export type ComponentState = 'default' | 'loading' | 'disabled' | 'error' | 'success'

/** 位置类型 */
export type Position = 'top' | 'bottom' | 'left' | 'right' | 'center'

/** 对齐方式 */
export type Alignment = 'start' | 'center' | 'end' | 'stretch'

/** 方向 */
export type Direction = 'horizontal' | 'vertical'

// ==========================================================================
// 按钮组件类型 - Button Component Types
// ==========================================================================

/** 按钮类型 */
export type ButtonType = 'button' | 'submit' | 'reset'

/** 按钮变体 */
export type ButtonVariant = 'filled' | 'outlined' | 'text' | 'ghost'

/** 按钮配置 */
export interface ButtonProps {
  /** 按钮类型 */
  type?: ButtonType
  /** 按钮变体 */
  variant?: ButtonVariant
  /** 颜色主题 */
  color?: ComponentVariant
  /** 大小 */
  size?: ComponentSize
  /** 是否禁用 */
  disabled?: boolean
  /** 是否加载中 */
  loading?: boolean
  /** 图标 */
  icon?: string | Component
  /** 图标位置 */
  iconPosition?: 'left' | 'right'
  /** 是否块级 */
  block?: boolean
  /** 是否圆形 */
  round?: boolean
  /** 点击事件 */
  onClick?: (event: MouseEvent) => void
}

// ==========================================================================
// 输入组件类型 - Input Component Types
// ==========================================================================

/** 输入框类型 */
export type InputType = 'text' | 'password' | 'email' | 'number' | 'tel' | 'url' | 'search'

/** 输入框配置 */
export interface InputProps {
  /** 输入类型 */
  type?: InputType
  /** 值 */
  value?: string | number
  /** 占位符 */
  placeholder?: string
  /** 是否禁用 */
  disabled?: boolean
  /** 是否只读 */
  readonly?: boolean
  /** 是否必填 */
  required?: boolean
  /** 最大长度 */
  maxLength?: number
  /** 最小长度 */
  minLength?: number
  /** 大小 */
  size?: ComponentSize
  /** 是否显示清除按钮 */
  clearable?: boolean
  /** 前缀图标 */
  prefixIcon?: string | Component
  /** 后缀图标 */
  suffixIcon?: string | Component
  /** 错误信息 */
  error?: string
  /** 帮助文本 */
  helpText?: string
  /** 输入事件 */
  onInput?: (value: string) => void
  /** 变化事件 */
  onChange?: (value: string) => void
  /** 焦点事件 */
  onFocus?: (event: FocusEvent) => void
  /** 失焦事件 */
  onBlur?: (event: FocusEvent) => void
}

// ==========================================================================
// 选择组件类型 - Select Component Types
// ==========================================================================

/** 选择器选项 */
export interface SelectOption<T = any> {
  /** 选项值 */
  value: T
  /** 选项标签 */
  label: string
  /** 是否禁用 */
  disabled?: boolean
  /** 图标 */
  icon?: string | Component
  /** 描述 */
  description?: string
  /** 分组 */
  group?: string
}

/** 选择器配置 */
export interface SelectProps<T = any> {
  /** 选项列表 */
  options: SelectOption<T>[]
  /** 选中值 */
  value?: T | T[]
  /** 占位符 */
  placeholder?: string
  /** 是否多选 */
  multiple?: boolean
  /** 是否可搜索 */
  searchable?: boolean
  /** 是否可清除 */
  clearable?: boolean
  /** 是否禁用 */
  disabled?: boolean
  /** 大小 */
  size?: ComponentSize
  /** 最大选择数量 */
  maxCount?: number
  /** 错误信息 */
  error?: string
  /** 加载状态 */
  loading?: boolean
  /** 选择事件 */
  onSelect?: (value: T | T[]) => void
  /** 搜索事件 */
  onSearch?: (query: string) => void
}

// ==========================================================================
// 模态框组件类型 - Modal Component Types
// ==========================================================================

/** 模态框大小 */
export type ModalSize = 'small' | 'medium' | 'large' | 'fullscreen'

/** 模态框配置 */
export interface ModalProps {
  /** 是否可见 */
  visible: boolean
  /** 标题 */
  title?: string
  /** 大小 */
  size?: ModalSize
  /** 是否可关闭 */
  closable?: boolean
  /** 是否点击遮罩关闭 */
  maskClosable?: boolean
  /** 是否显示页脚 */
  showFooter?: boolean
  /** 确认按钮文本 */
  confirmText?: string
  /** 取消按钮文本 */
  cancelText?: string
  /** 确认按钮加载状态 */
  confirmLoading?: boolean
  /** 关闭事件 */
  onClose?: () => void
  /** 确认事件 */
  onConfirm?: () => void | Promise<void>
  /** 取消事件 */
  onCancel?: () => void
}

// ==========================================================================
// 表格组件类型 - Table Component Types
// ==========================================================================

/** 表格列配置 */
export interface TableColumn<T = any> {
  /** 列键 */
  key: string
  /** 列标题 */
  title: string
  /** 数据索引 */
  dataIndex?: string
  /** 列宽 */
  width?: number | string
  /** 最小宽度 */
  minWidth?: number
  /** 是否固定 */
  fixed?: 'left' | 'right'
  /** 对齐方式 */
  align?: 'left' | 'center' | 'right'
  /** 是否可排序 */
  sortable?: boolean
  /** 是否可筛选 */
  filterable?: boolean
  /** 筛选选项 */
  filters?: Array<{ text: string; value: any }>
  /** 自定义渲染 */
  render?: (value: any, record: T, index: number) => VNode | string
  /** 自定义头部渲染 */
  renderHeader?: (column: TableColumn<T>) => VNode | string
}

/** 表格配置 */
export interface TableProps<T = any> {
  /** 数据源 */
  data: T[]
  /** 列配置 */
  columns: TableColumn<T>[]
  /** 行键 */
  rowKey?: string | ((record: T) => string)
  /** 是否显示边框 */
  bordered?: boolean
  /** 是否显示斑马纹 */
  striped?: boolean
  /** 是否可选择 */
  selectable?: boolean
  /** 选中的行 */
  selectedRows?: T[]
  /** 加载状态 */
  loading?: boolean
  /** 空数据文本 */
  emptyText?: string
  /** 分页配置 */
  pagination?: TablePaginationConfig
  /** 行点击事件 */
  onRowClick?: (record: T, index: number) => void
  /** 选择变化事件 */
  onSelectionChange?: (selectedRows: T[]) => void
  /** 排序变化事件 */
  onSortChange?: (column: TableColumn<T>, direction: 'asc' | 'desc' | null) => void
}

/** 表格分页配置 */
export interface TablePaginationConfig {
  /** 当前页 */
  current: number
  /** 每页大小 */
  pageSize: number
  /** 总数 */
  total: number
  /** 是否显示总数 */
  showTotal?: boolean
  /** 是否显示快速跳转 */
  showQuickJumper?: boolean
  /** 是否显示页面大小选择器 */
  showSizeChanger?: boolean
  /** 页面大小选项 */
  pageSizeOptions?: number[]
  /** 页面变化事件 */
  onChange?: (page: number, pageSize: number) => void
}

// ==========================================================================
// 表单组件类型 - Form Component Types
// ==========================================================================

/** 表单项配置 */
export interface FormItemProps {
  /** 字段名 */
  name: string
  /** 标签 */
  label?: string
  /** 是否必填 */
  required?: boolean
  /** 验证规则 */
  rules?: FormRule[]
  /** 错误信息 */
  error?: string
  /** 帮助文本 */
  helpText?: string
  /** 标签宽度 */
  labelWidth?: number | string
  /** 标签对齐方式 */
  labelAlign?: 'left' | 'right' | 'top'
}

/** 表单验证规则 */
export interface FormRule {
  /** 规则类型 */
  type?: 'required' | 'email' | 'url' | 'number' | 'integer' | 'float' | 'array' | 'object' | 'enum' | 'date' | 'boolean' | 'string' | 'method' | 'regexp'
  /** 是否必填 */
  required?: boolean
  /** 最小值/长度 */
  min?: number
  /** 最大值/长度 */
  max?: number
  /** 长度 */
  len?: number
  /** 正则表达式 */
  pattern?: RegExp
  /** 枚举值 */
  enum?: any[]
  /** 错误消息 */
  message?: string
  /** 自定义验证器 */
  validator?: (rule: FormRule, value: any) => boolean | string | Promise<boolean | string>
  /** 触发方式 */
  trigger?: 'change' | 'blur' | 'submit'
}

// ==========================================================================
// 通知组件类型 - Notification Component Types
// ==========================================================================

/** 通知位置 */
export type NotificationPosition = 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right' | 'top-center' | 'bottom-center'

/** 通知配置 */
export interface NotificationConfig {
  /** 通知ID */
  id?: string
  /** 标题 */
  title: string
  /** 消息内容 */
  message?: string
  /** 通知类型 */
  type?: ComponentVariant
  /** 持续时间（毫秒） */
  duration?: number
  /** 是否可关闭 */
  closable?: boolean
  /** 图标 */
  icon?: string | Component
  /** 位置 */
  position?: NotificationPosition
  /** 点击事件 */
  onClick?: () => void
  /** 关闭事件 */
  onClose?: () => void
}

// ==========================================================================
// 加载组件类型 - Loading Component Types
// ==========================================================================

/** 加载器类型 */
export type LoaderType = 'spinner' | 'dots' | 'pulse' | 'bars' | 'circle'

/** 加载配置 */
export interface LoadingProps {
  /** 是否显示 */
  visible: boolean
  /** 加载器类型 */
  type?: LoaderType
  /** 大小 */
  size?: ComponentSize
  /** 颜色 */
  color?: string
  /** 加载文本 */
  text?: string
  /** 是否全屏 */
  fullscreen?: boolean
  /** 背景颜色 */
  backgroundColor?: string
  /** 透明度 */
  opacity?: number
}

// ==========================================================================
// 工具提示组件类型 - Tooltip Component Types
// ==========================================================================

/** 工具提示位置 */
export type TooltipPlacement = 'top' | 'bottom' | 'left' | 'right' | 'top-start' | 'top-end' | 'bottom-start' | 'bottom-end' | 'left-start' | 'left-end' | 'right-start' | 'right-end'

/** 工具提示触发方式 */
export type TooltipTrigger = 'hover' | 'click' | 'focus' | 'manual'

/** 工具提示配置 */
export interface TooltipProps {
  /** 提示内容 */
  content: string | VNode
  /** 位置 */
  placement?: TooltipPlacement
  /** 触发方式 */
  trigger?: TooltipTrigger
  /** 是否可见 */
  visible?: boolean
  /** 是否禁用 */
  disabled?: boolean
  /** 延迟显示（毫秒） */
  showDelay?: number
  /** 延迟隐藏（毫秒） */
  hideDelay?: number
  /** 箭头是否可见 */
  showArrow?: boolean
  /** 最大宽度 */
  maxWidth?: number | string
}

// ==========================================================================
// 组件引用类型 - Component Ref Types
// ==========================================================================

/** 组件实例引用 */
export type ComponentRef<T = any> = Ref<T | null>

/** 表单组件引用 */
export interface FormRef {
  /** 验证表单 */
  validate(): Promise<boolean>
  /** 重置表单 */
  reset(): void
  /** 清除验证 */
  clearValidation(): void
  /** 获取表单值 */
  getValues(): Record<string, any>
  /** 设置表单值 */
  setValues(values: Record<string, any>): void
}

/** 表格组件引用 */
export interface TableRef {
  /** 刷新表格 */
  refresh(): void
  /** 清除选择 */
  clearSelection(): void
  /** 获取选中行 */
  getSelectedRows(): any[]
  /** 滚动到指定行 */
  scrollToRow(index: number): void
}

// ==========================================================================
// 通知组件类型 - Notification Component Types
// ==========================================================================

/** 通知类型 */
export type NotificationType = 'success' | 'error' | 'warning' | 'info'

/** 通知位置 */
export type NotificationPosition =
  | 'top-left' | 'top-center' | 'top-right'
  | 'bottom-left' | 'bottom-center' | 'bottom-right'

/** 通知动作接口 */
export interface NotificationAction {
  /** 动作标签 */
  label: string
  /** 动作类型 */
  type?: 'primary' | 'secondary' | 'default'
  /** 动作处理器 */
  handler: () => void
}

/** 通知组件属性 */
export interface NotificationProps {
  /** 通知类型 */
  type?: NotificationType
  /** 标题 */
  title?: string
  /** 消息内容 */
  message: string
  /** 显示时长（毫秒），0表示不自动关闭 */
  duration?: number
  /** 显示位置 */
  position?: NotificationPosition
  /** 是否可关闭 */
  closable?: boolean
  /** 动作按钮 */
  actions?: NotificationAction[]
  /** 是否显示进度条 */
  progress?: boolean
  /** 鼠标悬停时暂停 */
  pauseOnHover?: boolean
}
