import { BaseService } from '../base/BaseService'
import type { StorageResult, ServiceResponse } from '@/types'

/**
 * 存储服务
 * 提供统一的本地存储接口，支持数据序列化和错误处理
 */
export class StorageService extends BaseService {
  private static instance: StorageService
  private readonly prefix: string = 'money-app-'

  private constructor() {
    super()
  }

  /**
   * 获取单例实例
   */
  public static getInstance(): StorageService {
    if (!StorageService.instance) {
      StorageService.instance = new StorageService()
    }
    return StorageService.instance
  }

  /**
   * 存储数据
   */
  public async setItem<T>(key: string, value: T): Promise<ServiceResponse<void>> {
    return this.safeExecute(async () => {
      const fullKey = this.getFullKey(key)
      const serializedValue = JSON.stringify(value)
      localStorage.setItem(fullKey, serializedValue)
      this.log('info', `Data stored successfully`, { key: fullKey })
    }, 'STORAGE_SET_ERROR')
  }

  /**
   * 获取数据
   */
  public async getItem<T>(key: string, defaultValue?: T): Promise<ServiceResponse<T | null>> {
    return this.safeExecute(async () => {
      const fullKey = this.getFullKey(key)
      const item = localStorage.getItem(fullKey)
      
      if (item === null) {
        return defaultValue ?? null
      }

      try {
        return JSON.parse(item) as T
      } catch (parseError) {
        this.log('warn', `Failed to parse stored data, returning default`, { 
          key: fullKey, 
          error: parseError 
        })
        return defaultValue ?? null
      }
    }, 'STORAGE_GET_ERROR')
  }

  /**
   * 删除数据
   */
  public async removeItem(key: string): Promise<ServiceResponse<void>> {
    return this.safeExecute(async () => {
      const fullKey = this.getFullKey(key)
      localStorage.removeItem(fullKey)
      this.log('info', `Data removed successfully`, { key: fullKey })
    }, 'STORAGE_REMOVE_ERROR')
  }

  /**
   * 清空所有应用数据
   */
  public async clear(): Promise<ServiceResponse<void>> {
    return this.safeExecute(async () => {
      const keys = this.getAllAppKeys()
      keys.forEach(key => localStorage.removeItem(key))
      this.log('info', `All app data cleared`, { removedKeys: keys.length })
    }, 'STORAGE_CLEAR_ERROR')
  }

  /**
   * 检查键是否存在
   */
  public async hasItem(key: string): Promise<ServiceResponse<boolean>> {
    return this.safeExecute(async () => {
      const fullKey = this.getFullKey(key)
      return localStorage.getItem(fullKey) !== null
    }, 'STORAGE_HAS_ERROR')
  }

  /**
   * 获取存储大小（字节）
   */
  public async getStorageSize(): Promise<ServiceResponse<number>> {
    return this.safeExecute(async () => {
      const keys = this.getAllAppKeys()
      let totalSize = 0
      
      keys.forEach(key => {
        const value = localStorage.getItem(key)
        if (value) {
          totalSize += new Blob([value]).size
        }
      })
      
      return totalSize
    }, 'STORAGE_SIZE_ERROR')
  }

  /**
   * 获取所有应用相关的键
   */
  public async getAllKeys(): Promise<ServiceResponse<string[]>> {
    return this.safeExecute(async () => {
      return this.getAllAppKeys().map(key => key.replace(this.prefix, ''))
    }, 'STORAGE_KEYS_ERROR')
  }

  /**
   * 批量操作
   */
  public async batchSet<T>(items: Record<string, T>): Promise<ServiceResponse<void>> {
    return this.safeExecute(async () => {
      for (const [key, value] of Object.entries(items)) {
        const result = await this.setItem(key, value)
        if (!result.success) {
          throw new Error(`Failed to set item: ${key}`)
        }
      }
    }, 'STORAGE_BATCH_SET_ERROR')
  }

  /**
   * 批量获取
   */
  public async batchGet<T>(keys: string[]): Promise<ServiceResponse<Record<string, T | null>>> {
    return this.safeExecute(async () => {
      const result: Record<string, T | null> = {}
      
      for (const key of keys) {
        const itemResult = await this.getItem<T>(key)
        result[key] = itemResult.success ? itemResult.data! : null
      }
      
      return result
    }, 'STORAGE_BATCH_GET_ERROR')
  }

  /**
   * 获取完整键名
   */
  private getFullKey(key: string): string {
    return `${this.prefix}${key}`
  }

  /**
   * 获取所有应用相关的键
   */
  private getAllAppKeys(): string[] {
    const keys: string[] = []
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i)
      if (key && key.startsWith(this.prefix)) {
        keys.push(key)
      }
    }
    return keys
  }
}

// 导出单例实例
export const storageService = StorageService.getInstance()
