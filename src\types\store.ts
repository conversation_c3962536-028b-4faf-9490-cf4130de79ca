/**
 * 状态管理相关类型定义
 * Store related type definitions
 */

import type { Ref, ComputedRef } from 'vue'
import type { Transaction, Category, AppSettings, AsyncState } from './index'

// ==========================================================================
// Store 基础类型 - Store Base Types
// ==========================================================================

/** Store 状态 */
export type StoreState = 'idle' | 'loading' | 'success' | 'error'

/** Store 操作类型 */
export type StoreAction = 'create' | 'read' | 'update' | 'delete' | 'list' | 'search'

/** Store 错误 */
export interface StoreError {
  /** 错误代码 */
  code: string
  /** 错误消息 */
  message: string
  /** 错误详情 */
  details?: any
  /** 发生时间 */
  timestamp: number
  /** 相关操作 */
  action?: StoreAction
}

/** Store 操作结果 */
export interface StoreOperationResult<T = any> {
  /** 是否成功 */
  success: boolean
  /** 结果数据 */
  data?: T
  /** 错误信息 */
  error?: StoreError
  /** 操作时间戳 */
  timestamp: number
}

// ==========================================================================
// 交易 Store 类型 - Transaction Store Types
// ==========================================================================

/** 交易 Store 状态 */
export interface TransactionStoreState {
  /** 交易列表状态 */
  transactions: AsyncState<Transaction[]>
  /** 当前交易 */
  currentTransaction: Transaction | null
  /** 筛选条件 */
  filters: {
    /** 交易类型 */
    type?: 'income' | 'expense'
    /** 分类ID列表 */
    categoryIds: string[]
    /** 日期范围 */
    dateRange: {
      start: string | null
      end: string | null
    }
    /** 金额范围 */
    amountRange: {
      min: number | null
      max: number | null
    }
    /** 搜索关键词 */
    keyword: string
  }
  /** 排序配置 */
  sorting: {
    /** 排序字段 */
    field: keyof Transaction
    /** 排序方向 */
    direction: 'asc' | 'desc'
  }
  /** 分页配置 */
  pagination: {
    /** 当前页 */
    page: number
    /** 每页大小 */
    pageSize: number
    /** 总数 */
    total: number
  }
}

/** 交易 Store Getters */
export interface TransactionStoreGetters {
  /** 过滤后的交易列表 */
  filteredTransactions: ComputedRef<Transaction[]>
  /** 收入交易列表 */
  incomeTransactions: ComputedRef<Transaction[]>
  /** 支出交易列表 */
  expenseTransactions: ComputedRef<Transaction[]>
  /** 总收入 */
  totalIncome: ComputedRef<number>
  /** 总支出 */
  totalExpense: ComputedRef<number>
  /** 余额 */
  balance: ComputedRef<number>
  /** 交易统计 */
  statistics: ComputedRef<{
    totalTransactions: number
    averageIncome: number
    averageExpense: number
    transactionsByCategory: Record<string, number>
    transactionsByMonth: Record<string, number>
  }>
  /** 是否有更多数据 */
  hasMore: ComputedRef<boolean>
  /** 加载状态 */
  isLoading: ComputedRef<boolean>
  /** 错误状态 */
  hasError: ComputedRef<boolean>
}

/** 交易 Store Actions */
export interface TransactionStoreActions {
  /** 加载交易列表 */
  loadTransactions(forceRefresh?: boolean): Promise<void>
  /** 创建交易 */
  createTransaction(transaction: Omit<Transaction, 'id' | 'createdAt' | 'updatedAt'>): Promise<StoreOperationResult<Transaction>>
  /** 更新交易 */
  updateTransaction(id: string, updates: Partial<Transaction>): Promise<StoreOperationResult<Transaction>>
  /** 删除交易 */
  deleteTransaction(id: string): Promise<StoreOperationResult<void>>
  /** 批量删除交易 */
  deleteTransactions(ids: string[]): Promise<StoreOperationResult<void>>
  /** 搜索交易 */
  searchTransactions(query: string): Promise<void>
  /** 设置筛选条件 */
  setFilters(filters: Partial<TransactionStoreState['filters']>): void
  /** 重置筛选条件 */
  resetFilters(): void
  /** 设置排序 */
  setSorting(field: keyof Transaction, direction: 'asc' | 'desc'): void
  /** 设置分页 */
  setPagination(page: number, pageSize?: number): void
  /** 加载更多 */
  loadMore(): Promise<void>
  /** 刷新数据 */
  refresh(): Promise<void>
  /** 清除错误 */
  clearError(): void
}

// ==========================================================================
// 分类 Store 类型 - Category Store Types
// ==========================================================================

/** 分类 Store 状态 */
export interface CategoryStoreState {
  /** 分类列表状态 */
  categories: AsyncState<Category[]>
  /** 当前分类 */
  currentCategory: Category | null
  /** 分类使用统计 */
  usageStats: Record<string, {
    count: number
    totalAmount: number
    lastUsed: number
  }>
}

/** 分类 Store Getters */
export interface CategoryStoreGetters {
  /** 收入分类列表 */
  incomeCategories: ComputedRef<Category[]>
  /** 支出分类列表 */
  expenseCategories: ComputedRef<Category[]>
  /** 默认分类 */
  defaultCategories: ComputedRef<Category[]>
  /** 自定义分类 */
  customCategories: ComputedRef<Category[]>
  /** 按使用频率排序的分类 */
  categoriesByUsage: ComputedRef<Category[]>
  /** 分类映射 */
  categoryMap: ComputedRef<Record<string, Category>>
  /** 获取分类 */
  getCategoryById: (id: string) => Category | undefined
  /** 获取分类名称 */
  getCategoryName: (id: string) => string
}

/** 分类 Store Actions */
export interface CategoryStoreActions {
  /** 加载分类列表 */
  loadCategories(forceRefresh?: boolean): Promise<void>
  /** 创建分类 */
  createCategory(category: Omit<Category, 'id' | 'createdAt' | 'updatedAt'>): Promise<StoreOperationResult<Category>>
  /** 更新分类 */
  updateCategory(id: string, updates: Partial<Category>): Promise<StoreOperationResult<Category>>
  /** 删除分类 */
  deleteCategory(id: string): Promise<StoreOperationResult<void>>
  /** 重新排序分类 */
  reorderCategories(categoryIds: string[]): Promise<StoreOperationResult<void>>
  /** 更新使用统计 */
  updateUsageStats(categoryId: string, amount: number): void
  /** 初始化默认分类 */
  initializeDefaultCategories(): Promise<void>
  /** 刷新数据 */
  refresh(): Promise<void>
  /** 清除错误 */
  clearError(): void
}

// ==========================================================================
// 设置 Store 类型 - Settings Store Types
// ==========================================================================

/** 设置 Store 状态 */
export interface SettingsStoreState {
  /** 应用设置状态 */
  settings: AsyncState<AppSettings>
  /** 临时设置（未保存） */
  tempSettings: Partial<AppSettings>
  /** 是否有未保存的更改 */
  hasUnsavedChanges: boolean
}

/** 设置 Store Getters */
export interface SettingsStoreGetters {
  /** 当前主题 */
  currentTheme: ComputedRef<string>
  /** 当前货币 */
  currentCurrency: ComputedRef<string>
  /** 当前语言 */
  currentLanguage: ComputedRef<string>
  /** 是否启用通知 */
  notificationsEnabled: ComputedRef<boolean>
  /** 是否启用生物识别 */
  biometricEnabled: ComputedRef<boolean>
  /** 备份设置 */
  backupSettings: ComputedRef<AppSettings['backup']>
  /** 是否为暗色主题 */
  isDarkTheme: ComputedRef<boolean>
}

/** 设置 Store Actions */
export interface SettingsStoreActions {
  /** 加载设置 */
  loadSettings(forceRefresh?: boolean): Promise<void>
  /** 更新设置 */
  updateSettings(updates: Partial<AppSettings>): Promise<StoreOperationResult<AppSettings>>
  /** 重置设置 */
  resetSettings(): Promise<StoreOperationResult<AppSettings>>
  /** 导出设置 */
  exportSettings(): Promise<StoreOperationResult<string>>
  /** 导入设置 */
  importSettings(data: string): Promise<StoreOperationResult<AppSettings>>
  /** 设置主题 */
  setTheme(theme: AppSettings['theme']): Promise<void>
  /** 设置货币 - 已移除，固定使用人民币 */
  // setCurrency 方法已移除
  /** 设置语言 */
  setLanguage(language: AppSettings['language']): Promise<void>
  /** 切换通知 */
  toggleNotifications(): Promise<void>
  /** 切换生物识别 */
  toggleBiometric(): Promise<void>
  /** 保存临时设置 */
  saveTempSettings(): Promise<void>
  /** 丢弃临时设置 */
  discardTempSettings(): void
  /** 刷新数据 */
  refresh(): Promise<void>
  /** 清除错误 */
  clearError(): void
}

// ==========================================================================
// UI Store 类型 - UI Store Types
// ==========================================================================

/** UI Store 状态 */
export interface UIStoreState {
  /** 模态框状态 */
  modals: Record<string, {
    visible: boolean
    data?: any
  }>
  /** 抽屉状态 */
  drawers: Record<string, {
    visible: boolean
    data?: any
  }>
  /** 通知列表 */
  notifications: Array<{
    id: string
    type: 'success' | 'error' | 'warning' | 'info'
    title: string
    message?: string
    duration?: number
    timestamp: number
  }>
  /** 加载状态 */
  loading: Record<string, boolean>
  /** 确认对话框 */
  confirmDialog: {
    visible: boolean
    title: string
    message: string
    type: 'info' | 'warning' | 'error'
    onConfirm?: () => void
    onCancel?: () => void
  }
  /** 页面标题 */
  pageTitle: string
  /** 面包屑 */
  breadcrumbs: Array<{
    label: string
    path?: string
  }>
  /** 侧边栏状态 */
  sidebar: {
    collapsed: boolean
    visible: boolean
  }
}

/** UI Store Getters */
export interface UIStoreGetters {
  /** 是否有可见的模态框 */
  hasVisibleModal: ComputedRef<boolean>
  /** 是否有可见的抽屉 */
  hasVisibleDrawer: ComputedRef<boolean>
  /** 未读通知数量 */
  unreadNotificationCount: ComputedRef<number>
  /** 是否有加载状态 */
  hasLoading: ComputedRef<boolean>
  /** 获取模态框状态 */
  getModalState: (name: string) => ComputedRef<{ visible: boolean; data?: any }>
  /** 获取抽屉状态 */
  getDrawerState: (name: string) => ComputedRef<{ visible: boolean; data?: any }>
  /** 获取加载状态 */
  getLoadingState: (key: string) => ComputedRef<boolean>
}

/** UI Store Actions */
export interface UIStoreActions {
  /** 显示模态框 */
  showModal(name: string, data?: any): void
  /** 隐藏模态框 */
  hideModal(name: string): void
  /** 切换模态框 */
  toggleModal(name: string, data?: any): void
  /** 显示抽屉 */
  showDrawer(name: string, data?: any): void
  /** 隐藏抽屉 */
  hideDrawer(name: string): void
  /** 切换抽屉 */
  toggleDrawer(name: string, data?: any): void
  /** 显示通知 */
  showNotification(notification: Omit<UIStoreState['notifications'][0], 'id' | 'timestamp'>): string
  /** 隐藏通知 */
  hideNotification(id: string): void
  /** 清除所有通知 */
  clearNotifications(): void
  /** 设置加载状态 */
  setLoading(key: string, loading: boolean): void
  /** 显示确认对话框 */
  showConfirmDialog(config: Omit<UIStoreState['confirmDialog'], 'visible'>): Promise<boolean>
  /** 隐藏确认对话框 */
  hideConfirmDialog(): void
  /** 设置页面标题 */
  setPageTitle(title: string): void
  /** 设置面包屑 */
  setBreadcrumbs(breadcrumbs: UIStoreState['breadcrumbs']): void
  /** 切换侧边栏 */
  toggleSidebar(): void
  /** 设置侧边栏状态 */
  setSidebarState(state: Partial<UIStoreState['sidebar']>): void
}

// ==========================================================================
// Store 组合类型 - Store Composition Types
// ==========================================================================

/** 根 Store 类型 */
export interface RootStore {
  /** 交易 Store */
  transactions: TransactionStoreState & TransactionStoreGetters & TransactionStoreActions
  /** 分类 Store */
  categories: CategoryStoreState & CategoryStoreGetters & CategoryStoreActions
  /** 设置 Store */
  settings: SettingsStoreState & SettingsStoreGetters & SettingsStoreActions
  /** UI Store */
  ui: UIStoreState & UIStoreGetters & UIStoreActions
}

/** Store 插件配置 */
export interface StorePluginConfig {
  /** 插件名称 */
  name: string
  /** 是否启用 */
  enabled: boolean
  /** 插件选项 */
  options?: Record<string, any>
}

/** Store 持久化配置 */
export interface StorePersistConfig {
  /** 存储键 */
  key: string
  /** 存储引擎 */
  storage: 'localStorage' | 'sessionStorage' | 'indexedDB'
  /** 包含的路径 */
  paths?: string[]
  /** 排除的路径 */
  excludePaths?: string[]
  /** 序列化函数 */
  serializer?: {
    serialize: (value: any) => string
    deserialize: (value: string) => any
  }
}
