/* ==========================================================================
   设计系统 - Design System
   ========================================================================== */

:root {
  /* 颜色系统 */
  --color-primary: #3b82f6;
  --color-primary-hover: #2563eb;
  --color-success: #10b981;
  --color-success-hover: #059669;
  --color-danger: #ef4444;
  --color-danger-hover: #dc2626;
  --color-warning: #f59e0b;
  --color-warning-hover: #d97706;
  
  /* 语义化颜色 */
  --color-income: var(--color-success);
  --color-expense: var(--color-danger);
  
  /* 间距系统 */
  --spacing-xs: 0.25rem;    /* 4px */
  --spacing-sm: 0.5rem;     /* 8px */
  --spacing-md: 1rem;       /* 16px */
  --spacing-lg: 1.5rem;     /* 24px */
  --spacing-xl: 2rem;       /* 32px */
  --spacing-2xl: 3rem;      /* 48px */
  
  /* 圆角系统 */
  --radius-sm: 0.375rem;    /* 6px */
  --radius-md: 0.5rem;      /* 8px */
  --radius-lg: 0.75rem;     /* 12px */
  --radius-xl: 1rem;        /* 16px */
  --radius-2xl: 1.5rem;     /* 24px */
  
  /* 阴影系统已移除 - 使用边框和背景色创建层次 */
  
  /* 过渡动画 */
  --transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-normal: 250ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-slow: 350ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-bounce: 400ms cubic-bezier(0.68, -0.55, 0.265, 1.55);
  
  /* 字体大小 */
  --text-xs: 0.75rem;       /* 12px */
  --text-sm: 0.875rem;      /* 14px */
  --text-base: 1rem;        /* 16px */
  --text-lg: 1.125rem;      /* 18px */
  --text-xl: 1.25rem;       /* 20px */
  --text-2xl: 1.5rem;       /* 24px */
  --text-3xl: 1.875rem;     /* 30px */
  
  /* Z-index 层级 */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
  --z-toast: 1080;
}

/* ==========================================================================
   组件样式 - Component Styles
   ========================================================================== */

/* 按钮系统 */
.btn {
  @apply inline-flex items-center justify-center px-4 py-2.5 text-sm font-medium rounded-2xl;
  @apply focus:outline-none focus:ring-2 focus:ring-offset-2;
  @apply disabled:opacity-50 disabled:cursor-not-allowed;
  transition: all var(--transition-normal);
  transform: translateZ(0); /* 启用硬件加速 */
  min-height: 44px; /* 移动端最小触摸区域 */
}

.btn:active {
  transform: scale(0.98) translateZ(0);
  transition: all var(--transition-fast);
}

.btn-primary {
  @apply btn bg-blue-500 text-white hover:bg-blue-600;
  @apply focus:ring-blue-500;
}

.btn-secondary {
  @apply btn bg-gray-100 text-gray-700 hover:bg-gray-200;
  @apply dark:bg-gray-800 dark:text-gray-300 dark:hover:bg-gray-700;
  @apply focus:ring-gray-500;
}

.btn-success {
  @apply btn bg-green-500 text-white hover:bg-green-600;
  @apply focus:ring-green-500;
}

.btn-danger {
  @apply btn bg-red-500 text-white hover:bg-red-600;
  @apply focus:ring-red-500;
}

.btn-ghost {
  @apply btn bg-transparent text-gray-700 hover:bg-gray-100;
  @apply dark:text-gray-300 dark:hover:bg-gray-800;
  @apply focus:ring-gray-500;
}

/* 输入框系统 */
.input {
  @apply block w-full px-3 py-2.5 text-base;
  @apply bg-white dark:bg-black;
  @apply border border-gray-200 dark:border-gray-700;
  @apply rounded-2xl;
  @apply focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500;
  @apply transition-colors duration-200;
  @apply placeholder-gray-400 dark:placeholder-gray-500;
  min-height: 44px; /* 移动端最小触摸区域 */
}

.input-error {
  @apply input border-red-300 focus:border-red-500 focus:ring-red-500;
}

/* 卡片系统 */
.card {
  @apply bg-white dark:bg-black rounded-2xl;
  @apply border border-gray-200 dark:border-gray-800;
}

.card-elevated {
  @apply card;
}

.card-interactive {
  @apply card cursor-pointer;
  transition: all var(--transition-normal);
  transform: translateZ(0);
}

.card-interactive:hover {
  @apply bg-gray-50 dark:bg-gray-900;
  transform: translateY(-1px) translateZ(0);
}

.card-interactive:active {
  transform: translateY(0) scale(0.99) translateZ(0);
  transition: all var(--transition-fast);
}

/* 模态框系统 */
.modal-backdrop {
  @apply fixed inset-0 flex items-center justify-center z-50 p-4;
}

.modal-content {
  @apply bg-white dark:bg-black rounded-2xl w-full max-w-sm p-6;
  @apply border border-gray-200 dark:border-gray-700;
  transform: translateZ(0);
  transition: all var(--transition-slow);
}

.modal-bottom-sheet {
  @apply fixed inset-0 flex items-end justify-center z-50;
}

.modal-bottom-content {
  @apply bg-white dark:bg-black rounded-t-3xl w-full max-w-md max-h-[85vh];
  @apply border border-gray-200 dark:border-gray-700 border-b-0;
  transform: translateZ(0);
  transition: transform var(--transition-slow);
  /* 隐藏滚动条但保持滚动功能 */
  overflow-y: auto;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
  /* 为全面屏机型优化底部高度 */
  padding-bottom: calc(env(safe-area-inset-bottom) + 2rem);
}

.modal-bottom-content::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera */
}

/* 通用滚动条隐藏类 */
.scrollbar-hide {
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}

.scrollbar-hide::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera */
}

/* 列表系统 */
.list-item {
  @apply flex items-center p-4 hover:bg-gray-50 dark:hover:bg-gray-900;
  @apply border-b border-gray-100 dark:border-gray-800 last:border-b-0;
  transition: all var(--transition-normal);
  transform: translateZ(0);
}

/* Vue过渡动画 */
.modal-enter-active,
.modal-leave-active {
  transition: opacity var(--transition-slow);
}

.modal-enter-from,
.modal-leave-to {
  opacity: 0;
}

.modal-enter-active .modal-content,
.modal-leave-active .modal-content {
  transition: all var(--transition-slow);
}

.modal-enter-from .modal-content,
.modal-leave-to .modal-content {
  transform: scale(0.9) translateY(20px) translateZ(0);
  opacity: 0;
}

.modal-enter-active .modal-bottom-content,
.modal-leave-active .modal-bottom-content {
  transition: transform var(--transition-slow);
}

.modal-enter-from .modal-bottom-content,
.modal-leave-to .modal-bottom-content {
  transform: translateY(100%) translateZ(0);
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity var(--transition-normal);
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.slide-up-enter-active,
.slide-up-leave-active {
  transition: all var(--transition-normal);
}

.slide-up-enter-from,
.slide-up-leave-to {
  transform: translateY(20px) translateZ(0);
  opacity: 0;
}

.list-item-interactive {
  @apply list-item cursor-pointer active:bg-gray-100 dark:active:bg-gray-800;
}

/* 徽章系统 */
.badge {
  @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
}

.badge-primary {
  @apply badge bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200;
}

.badge-success {
  @apply badge bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200;
}

.badge-danger {
  @apply badge bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200;
}

/* 分隔线 */
.divider {
  @apply border-t border-gray-200 dark:border-gray-700;
}

/* 安全区域适配 */
.safe-area-top {
  padding-top: env(safe-area-inset-top);
}

.safe-area-bottom {
  padding-bottom: env(safe-area-inset-bottom);
}

.safe-area-left {
  padding-left: env(safe-area-inset-left);
}

.safe-area-right {
  padding-right: env(safe-area-inset-right);
}

/* 页面容器 */
.page-container {
  @apply min-h-screen bg-gray-50 dark:bg-black;
  @apply pt-safe-top pb-safe-bottom;
}

/* 动画类 */
.animate-fade-in {
  animation: fadeIn 0.3s ease-out;
}

.animate-slide-up {
  animation: slideUp 0.3s ease-out;
}

.animate-scale-in {
  animation: scaleIn 0.2s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { 
    opacity: 0;
    transform: translateY(20px);
  }
  to { 
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from { 
    opacity: 0;
    transform: scale(0.95);
  }
  to { 
    opacity: 1;
    transform: scale(1);
  }
}

/* Vue 过渡动画 */
.modal-enter-active,
.modal-leave-active {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.modal-enter-from,
.modal-leave-to {
  opacity: 0;
  transform: scale(0.95) translateY(10px);
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.2s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
