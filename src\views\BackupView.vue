<template>
  <div class="page-container">
    <!-- 头部 -->
    <div class="sticky top-0 bg-gray-50 dark:bg-black border-b border-gray-200 dark:border-gray-800 px-4 py-4 safe-area-top">
      <div class="flex items-center justify-between">
        <button
          @click="goBack"
          class="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
        >
          <ArrowLeftIcon class="w-6 h-6" />
        </button>
        <h1 class="text-lg font-semibold">{{ $t('backup.title') }}</h1>
        <div class="w-10"></div>
      </div>
    </div>

    <!-- 内容 -->
    <div class="p-4 space-y-6">
      <!-- 备份设置 -->
      <BaseCard>
        <template #header>
          <h2 class="text-lg font-semibold">{{ $t('backup.settings') }}</h2>
        </template>
        
        <div class="space-y-4">
          <div class="flex items-center justify-between">
            <div>
              <p class="font-medium">{{ $t('backup.autoBackup') }}</p>
              <p class="text-sm text-gray-500 dark:text-gray-400">{{ $t('backup.autoBackupDesc') }}</p>
            </div>
            <label class="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                v-model="backupSettings.enabled"
                class="sr-only peer"
                @change="updateBackupSettings"
              >
              <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
            </label>
          </div>
          
          <div v-if="backupSettings.enabled" class="flex items-center justify-between">
            <div>
              <p class="font-medium">备份频率</p>
              <p class="text-sm text-gray-500 dark:text-gray-400">每 {{ backupSettings.frequency }} 天备份一次</p>
            </div>
            <select
              v-model="backupSettings.frequency"
              @change="updateBackupSettings"
              class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-2xl bg-white dark:bg-gray-800"
            >
              <option value="1">每天</option>
              <option value="3">每3天</option>
              <option value="7">每周</option>
              <option value="30">每月</option>
            </select>
          </div>
          
          <div v-if="backupSettings.lastBackup" class="text-sm text-gray-500 dark:text-gray-400">
            上次备份：{{ formatDateTime(backupSettings.lastBackup) }}
          </div>
        </div>
      </BaseCard>

      <!-- 手动备份 -->
      <BaseCard>
        <template #header>
          <h2 class="text-lg font-semibold">手动备份</h2>
        </template>
        
        <div class="space-y-4">
          <p class="text-gray-600 dark:text-gray-400">
            导出您的所有数据，包括交易记录和分类设置。
          </p>
          
          <BaseButton
            variant="primary"
            full-width
            @click="exportData"
            :loading="isExporting"
          >
            <CloudArrowDownIcon class="w-5 h-5 mr-2" />
            导出数据
          </BaseButton>
        </div>
      </BaseCard>

      <!-- 数据恢复 -->
      <BaseCard>
        <template #header>
          <h2 class="text-lg font-semibold">数据恢复</h2>
        </template>
        
        <div class="space-y-4">
          <p class="text-gray-600 dark:text-gray-400">
            从备份文件恢复您的数据。注意：这将覆盖当前所有数据。
          </p>
          
          <input
            ref="fileInput"
            type="file"
            accept=".json"
            @change="handleFileSelect"
            class="hidden"
          >
          
          <BaseButton
            variant="secondary"
            full-width
            @click="selectFile"
          >
            <CloudArrowUpIcon class="w-5 h-5 mr-2" />
            选择备份文件
          </BaseButton>
          
          <BaseButton
            v-if="selectedFile"
            variant="success"
            full-width
            @click="importData"
            :loading="isImporting"
          >
            <CheckIcon class="w-5 h-5 mr-2" />
            恢复数据
          </BaseButton>
        </div>
      </BaseCard>

      <!-- 危险操作 -->
      <BaseCard>
        <template #header>
          <h2 class="text-lg font-semibold text-red-600">危险操作</h2>
        </template>
        
        <div class="space-y-4">
          <p class="text-gray-600 dark:text-gray-400">
            清空所有数据，包括交易记录和自定义分类。此操作无法撤销。
          </p>
          
          <BaseButton
            variant="danger"
            full-width
            @click="showClearConfirm = true"
          >
            <TrashIcon class="w-5 h-5 mr-2" />
            清空所有数据
          </BaseButton>
        </div>
      </BaseCard>
    </div>

    <!-- 确认清空数据模态框 -->
    <BaseModal
      v-model="showClearConfirm"
      title="确认清空数据"
      size="sm"
    >
      <div class="text-center py-4">
        <div class="w-16 h-16 mx-auto mb-4 bg-red-100 dark:bg-red-900/20 rounded-full flex items-center justify-center">
          <ExclamationTriangleIcon class="w-8 h-8 text-red-500" />
        </div>
        <p class="text-gray-600 dark:text-gray-400 mb-6">
          确定要清空所有数据吗？这将删除所有交易记录和自定义分类，此操作无法撤销。
        </p>
        <div class="flex space-x-3">
          <BaseButton
            variant="secondary"
            full-width
            @click="showClearConfirm = false"
          >
            取消
          </BaseButton>
          <BaseButton
            variant="danger"
            full-width
            @click="clearAllData"
          >
            确认清空
          </BaseButton>
        </div>
      </div>
    </BaseModal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import {
  ArrowLeftIcon,
  CloudArrowDownIcon,
  CloudArrowUpIcon,
  CheckIcon,
  TrashIcon,
  ExclamationTriangleIcon
} from '@heroicons/vue/24/outline'
import { BaseButton, BaseCard, BaseModal } from '@/components/ui'
import { useTransactionStore } from '@/stores/transactions'
import { useSettingsStore } from '@/stores/settings'

const router = useRouter()
const transactionStore = useTransactionStore()
const settingsStore = useSettingsStore()
const { t } = useI18n()

const isExporting = ref(false)
const isImporting = ref(false)
const selectedFile = ref<File | null>(null)
const fileInput = ref<HTMLInputElement>()
const showClearConfirm = ref(false)

const backupSettings = computed({
  get: () => settingsStore.settings.backup,
  set: (value) => settingsStore.updateBackupSettings(value)
})

const formatDateTime = (dateString: string) => {
  return new Date(dateString).toLocaleString('zh-CN')
}

const goBack = () => {
  router.back()
}

const updateBackupSettings = () => {
  settingsStore.updateBackupSettings(backupSettings.value)
}

const exportData = async () => {
  isExporting.value = true
  try {
    const data = transactionStore.exportData()
    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `money-app-backup-${new Date().toISOString().split('T')[0]}.json`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
    
    // 更新最后备份时间
    settingsStore.updateBackupSettings({
      lastBackup: new Date().toISOString()
    })
  } catch (error) {
    console.error('Export failed:', error)
    alert('导出失败，请重试')
  } finally {
    isExporting.value = false
  }
}

const selectFile = () => {
  fileInput.value?.click()
}

const handleFileSelect = (event: Event) => {
  const target = event.target as HTMLInputElement
  selectedFile.value = target.files?.[0] || null
}

const importData = async () => {
  if (!selectedFile.value) return
  
  isImporting.value = true
  try {
    const text = await selectedFile.value.text()
    const data = JSON.parse(text)
    
    if (transactionStore.importData(data)) {
      alert('数据恢复成功！')
      selectedFile.value = null
      if (fileInput.value) {
        fileInput.value.value = ''
      }
    } else {
      alert('数据格式不正确，请检查备份文件')
    }
  } catch (error) {
    console.error('Import failed:', error)
    alert('数据恢复失败，请检查文件格式')
  } finally {
    isImporting.value = false
  }
}

const clearAllData = () => {
  transactionStore.clearAllData()
  showClearConfirm.value = false
  alert('所有数据已清空')
}
</script>
