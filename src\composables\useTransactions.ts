import { computed, ref, watch } from 'vue'
import { useTransactionStore } from '@/stores/transactions'
import type { Transaction, TransactionFilter, TransactionType } from '@/types'

/**
 * 交易数据管理组合式函数
 * 提供交易相关的响应式数据和操作方法
 */
export function useTransactions() {
  const store = useTransactionStore()

  // ==========================================================================
  // 响应式状态 - Reactive State
  // ==========================================================================

  /** 当前筛选条件 */
  const filter = ref<TransactionFilter>({})

  /** 是否显示加载状态 */
  const showLoading = ref(false)

  // ==========================================================================
  // 计算属性 - Computed Properties
  // ==========================================================================

  /** 筛选后的交易记录 */
  const filteredTransactions = computed(() => {
    if (Object.keys(filter.value).length === 0) {
      return store.sortedTransactions
    }
    return store.getFilteredTransactions(filter.value)
  })

  /** 当前月份的交易记录 */
  const currentMonthTransactions = computed(() => {
    const currentMonth = new Date().toISOString().substring(0, 7)
    return store.getTransactionsByMonth(currentMonth)
  })

  /** 最近的交易记录 */
  const recentTransactions = computed(() => store.recentTransactions)

  /** 交易统计信息 */
  const transactionStats = computed(() => {
    const transactions = filteredTransactions.value
    const totalIncome = transactions
      .filter(t => t.type === 'income')
      .reduce((sum, t) => sum + t.amount, 0)
    
    const totalExpense = transactions
      .filter(t => t.type === 'expense')
      .reduce((sum, t) => sum + t.amount, 0)

    return {
      totalIncome,
      totalExpense,
      balance: totalIncome - totalExpense,
      count: transactions.length
    }
  })

  /** 是否有数据 */
  const hasTransactions = computed(() => store.transactions.length > 0)

  /** 是否正在加载 */
  const isLoading = computed(() => store.isLoading || showLoading.value)

  /** 是否有错误 */
  const hasError = computed(() => store.hasError)

  /** 错误信息 */
  const errorMessage = computed(() => store.errorMessage)

  // ==========================================================================
  // 交易操作方法 - Transaction Actions
  // ==========================================================================

  /** 初始化交易数据 */
  async function initialize() {
    showLoading.value = true
    try {
      await store.initialize()
    } finally {
      showLoading.value = false
    }
  }

  /** 刷新交易数据 */
  async function refresh() {
    showLoading.value = true
    try {
      await store.refreshAll()
    } finally {
      showLoading.value = false
    }
  }

  /** 创建交易记录 */
  async function createTransaction(
    transactionData: Omit<Transaction, 'id' | 'createdAt' | 'updatedAt'>
  ) {
    showLoading.value = true
    try {
      const result = await store.createTransaction(transactionData)
      if (result) {
        return { success: true, data: result }
      } else {
        return { success: false, error: store.errorMessage }
      }
    } finally {
      showLoading.value = false
    }
  }

  /** 更新交易记录 */
  async function updateTransaction(
    id: string,
    updates: Partial<Omit<Transaction, 'id' | 'createdAt'>>
  ) {
    showLoading.value = true
    try {
      const success = await store.updateTransaction(id, updates)
      if (success) {
        return { success: true }
      } else {
        return { success: false, error: store.errorMessage }
      }
    } finally {
      showLoading.value = false
    }
  }

  /** 删除交易记录 */
  async function deleteTransaction(id: string) {
    showLoading.value = true
    try {
      const success = await store.deleteTransaction(id)
      if (success) {
        return { success: true }
      } else {
        return { success: false, error: store.errorMessage }
      }
    } finally {
      showLoading.value = false
    }
  }

  /** 批量删除交易记录 */
  async function deleteTransactions(ids: string[]) {
    showLoading.value = true
    try {
      const deletedCount = await store.deleteTransactions(ids)
      return { success: true, deletedCount }
    } finally {
      showLoading.value = false
    }
  }

  // ==========================================================================
  // 筛选和查询方法 - Filter and Query Methods
  // ==========================================================================

  /** 设置筛选条件 */
  function setFilter(newFilter: TransactionFilter) {
    filter.value = { ...newFilter }
    store.setFilter(newFilter)
  }

  /** 清除筛选条件 */
  function clearFilter() {
    filter.value = {}
    store.clearFilter()
  }

  /** 按类型筛选 */
  function filterByType(type: TransactionType | null) {
    if (type) {
      setFilter({ ...filter.value, type })
    } else {
      const { type: _, ...rest } = filter.value
      setFilter(rest)
    }
  }

  /** 按分类筛选 */
  function filterByCategory(categoryIds: string[]) {
    if (categoryIds.length > 0) {
      setFilter({ ...filter.value, categoryIds })
    } else {
      const { categoryIds: _, ...rest } = filter.value
      setFilter(rest)
    }
  }

  /** 按日期范围筛选 */
  function filterByDateRange(startDate?: string, endDate?: string) {
    const newFilter = { ...filter.value }
    
    if (startDate) {
      newFilter.startDate = startDate
    } else {
      delete newFilter.startDate
    }
    
    if (endDate) {
      newFilter.endDate = endDate
    } else {
      delete newFilter.endDate
    }
    
    setFilter(newFilter)
  }

  /** 按关键词搜索 */
  function searchByKeyword(keyword: string) {
    if (keyword.trim()) {
      setFilter({ ...filter.value, keyword: keyword.trim() })
    } else {
      const { keyword: _, ...rest } = filter.value
      setFilter(rest)
    }
  }

  /** 获取指定月份的统计 */
  function getMonthlyStats(monthString: string) {
    return store.getMonthlyStats(monthString)
  }

  /** 获取分类统计 */
  function getCategoryStats(type: TransactionType, monthString?: string) {
    return store.getCategoryStats(type, monthString)
  }

  // ==========================================================================
  // 工具方法 - Utility Methods
  // ==========================================================================

  /** 清除错误状态 */
  function clearError() {
    store.clearErrors()
  }

  /** 根据ID获取交易记录 */
  function getTransactionById(id: string) {
    return store.getTransactionById(id)
  }

  /** 获取指定分类的交易记录 */
  function getTransactionsByCategory(categoryId: string) {
    return store.getTransactionsByCategory(categoryId)
  }

  /** 获取指定月份的交易记录 */
  function getTransactionsByMonth(monthString: string) {
    return store.getTransactionsByMonth(monthString)
  }

  // ==========================================================================
  // 监听器 - Watchers
  // ==========================================================================

  // 监听筛选条件变化
  watch(filter, (newFilter) => {
    store.setFilter(newFilter)
  }, { deep: true })

  // ==========================================================================
  // 返回接口 - Return Interface
  // ==========================================================================

  return {
    // 响应式状态
    filter,
    isLoading,
    hasError,
    errorMessage,
    hasTransactions,

    // 计算属性
    filteredTransactions,
    currentMonthTransactions,
    recentTransactions,
    transactionStats,

    // 数据操作方法
    initialize,
    refresh,
    createTransaction,
    updateTransaction,
    deleteTransaction,
    deleteTransactions,

    // 筛选和查询方法
    setFilter,
    clearFilter,
    filterByType,
    filterByCategory,
    filterByDateRange,
    searchByKeyword,
    getMonthlyStats,
    getCategoryStats,

    // 工具方法
    clearError,
    getTransactionById,
    getTransactionsByCategory,
    getTransactionsByMonth
  }
}
