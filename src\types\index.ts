// ==========================================================================
// 核心数据类型 - Core Data Types
// ==========================================================================

/** 交易类型 */
export type TransactionType = 'income' | 'expense'

/** 主题模式 */
export type ThemeMode = 'light' | 'dark' | 'system'

/** 货币代码 - 固定使用人民币 */
export type CurrencyCode = 'CNY'

/** 语言代码 */
export type LanguageCode = 'zh-CN' | 'zh-TW' | 'en-US' | 'ja-JP' | 'ko-KR'

/** 排序方向 */
export type SortDirection = 'asc' | 'desc'

/** 日期格式类型 */
export type DateFormat = 'full' | 'short' | 'month' | 'year' | 'time' | 'datetime'

/** 通知类型 */
export type NotificationType = 'success' | 'error' | 'warning' | 'info'

/** 确认对话框类型 */
export type ConfirmDialogType = 'info' | 'warning' | 'error'

/** 数据导出格式 */
export type ExportFormat = 'json' | 'csv' | 'excel'

/** 备份状态 */
export type BackupStatus = 'idle' | 'backing-up' | 'restoring' | 'success' | 'error'

// ==========================================================================
// 交易相关类型 - Transaction Types
// ==========================================================================

/** 交易记录 */
export interface Transaction {
  /** 唯一标识符 */
  id: string
  /** 交易类型：收入或支出 */
  type: TransactionType
  /** 金额（分为单位，避免浮点数精度问题） */
  amount: number
  /** 分类ID */
  categoryId: string
  /** 描述 */
  description: string
  /** 交易日期 (YYYY-MM-DD) */
  date: string
  /** 创建时间 (ISO 8601) */
  createdAt: string
  /** 更新时间 (ISO 8601) */
  updatedAt: string
}

/** 分类 */
export interface Category {
  /** 唯一标识符 */
  id: string
  /** 分类名称 */
  name: string
  /** 图标（emoji 或图标名称） */
  icon: string
  /** 颜色（十六进制） */
  color: string
  /** 分类类型 */
  type: TransactionType
  /** 是否为默认分类 */
  isDefault: boolean
  /** 排序权重 */
  order: number
  /** 创建时间 (ISO 8601) */
  createdAt?: string
  /** 更新时间 (ISO 8601) */
  updatedAt?: string
}

/** 预算 */
export interface Budget {
  /** 唯一标识符 */
  id: string
  /** 预算名称 */
  name: string
  /** 预算金额（分为单位） */
  amount: number
  /** 已使用金额（分为单位） */
  spent: number
  /** 分类ID列表 */
  categoryIds: string[]
  /** 预算周期类型 */
  period: 'monthly' | 'weekly' | 'daily' | 'yearly'
  /** 开始日期 */
  startDate: string
  /** 结束日期 */
  endDate: string
  /** 是否启用 */
  enabled: boolean
  /** 警告阈值（百分比） */
  warningThreshold: number
  /** 创建时间 */
  createdAt: string
  /** 更新时间 */
  updatedAt: string
}

/** 标签 */
export interface Tag {
  /** 唯一标识符 */
  id: string
  /** 标签名称 */
  name: string
  /** 标签颜色 */
  color: string
  /** 使用次数 */
  usageCount: number
  /** 创建时间 */
  createdAt: string
}

// ==========================================================================
// 统计相关类型 - Statistics Types
// ==========================================================================

/** 月度统计 */
export interface MonthlyStats {
  /** 月份 (YYYY-MM) */
  month: string
  /** 总收入（分为单位） */
  totalIncome: number
  /** 总支出（分为单位） */
  totalExpense: number
  /** 余额（分为单位） */
  balance: number
  /** 交易数量 */
  transactionCount: number
}

/** 分类统计 */
export interface CategoryStats {
  /** 分类ID */
  categoryId: string
  /** 分类名称 */
  categoryName: string
  /** 分类图标 */
  categoryIcon: string
  /** 分类颜色 */
  categoryColor: string
  /** 总金额（分为单位） */
  amount: number
  /** 占比（百分比） */
  percentage: number
  /** 交易数量 */
  count: number
}

/** 趋势数据点 */
export interface TrendDataPoint {
  /** 日期 (YYYY-MM-DD) */
  date: string
  /** 收入金额 */
  income: number
  /** 支出金额 */
  expense: number
  /** 净收入 */
  net: number
}

/** 年度统计 */
export interface YearlyStats {
  /** 年份 */
  year: number
  /** 总收入 */
  totalIncome: number
  /** 总支出 */
  totalExpense: number
  /** 余额 */
  balance: number
  /** 交易数量 */
  transactionCount: number
  /** 月度数据 */
  monthlyData: Record<string, MonthlyStats>
  /** 平均月收入 */
  averageMonthlyIncome: number
  /** 平均月支出 */
  averageMonthlyExpense: number
}

/** 周统计 */
export interface WeeklyStats {
  /** 开始日期 */
  startDate: string
  /** 结束日期 */
  endDate: string
  /** 总收入 */
  totalIncome: number
  /** 总支出 */
  totalExpense: number
  /** 余额 */
  balance: number
  /** 交易数量 */
  transactionCount: number
  /** 日度数据 */
  dailyData: Record<string, DailyStats>
  /** 平均日收入 */
  averageDailyIncome: number
  /** 平均日支出 */
  averageDailyExpense: number
}

/** 日统计 */
export interface DailyStats {
  /** 日期 */
  date: string
  /** 总收入 */
  totalIncome: number
  /** 总支出 */
  totalExpense: number
  /** 余额 */
  balance: number
  /** 交易数量 */
  transactionCount: number
  /** 交易记录 */
  transactions: Transaction[]
}

/** 分类使用统计 */
export interface CategoryUsageStats {
  /** 分类信息 */
  category: Category
  /** 交易数量 */
  transactionCount: number
  /** 总金额 */
  totalAmount: number
  /** 最后使用时间 */
  lastUsed: number | null
  /** 使用频率（每月平均次数） */
  usageFrequency: number
}

// ==========================================================================
// 应用设置类型 - App Settings Types
// ==========================================================================

/** 应用设置 */
export interface AppSettings {
  /** 主题模式 */
  theme: ThemeMode
  /** 语言 */
  language: LanguageCode
  /** 是否启用通知 */
  notifications: boolean
  /** 是否启用生物识别 */
  biometric: boolean
  /** 数据备份设置 */
  backup: {
    /** 是否启用自动备份 */
    enabled: boolean
    /** 备份频率（天） */
    frequency: number
    /** 最后备份时间 */
    lastBackup?: string
  }
}

// ==========================================================================
// UI 相关类型 - UI Types
// ==========================================================================

/** 加载状态 */
export interface LoadingState {
  /** 是否正在加载 */
  loading: boolean
  /** 错误信息 */
  error?: string
}

/** 分页信息 */
export interface Pagination {
  /** 当前页码 */
  page: number
  /** 每页数量 */
  pageSize: number
  /** 总数量 */
  total: number
  /** 是否有更多数据 */
  hasMore: boolean
}

/** 筛选条件 */
export interface TransactionFilter {
  /** 交易类型 */
  type?: TransactionType
  /** 分类ID列表 */
  categoryIds?: string[]
  /** 开始日期 */
  startDate?: string
  /** 结束日期 */
  endDate?: string
  /** 最小金额 */
  minAmount?: number
  /** 最大金额 */
  maxAmount?: number
  /** 搜索关键词 */
  keyword?: string
  /** 标签ID列表 */
  tagIds?: string[]
}

/** 通知消息 */
export interface NotificationMessage {
  /** 唯一标识符 */
  id: string
  /** 通知类型 */
  type: NotificationType
  /** 标题 */
  title: string
  /** 消息内容 */
  message?: string
  /** 持续时间（毫秒） */
  duration?: number
  /** 是否持久化 */
  persistent?: boolean
  /** 创建时间 */
  createdAt: number
  /** 操作按钮 */
  actions?: NotificationAction[]
}

/** 通知操作 */
export interface NotificationAction {
  /** 操作标识 */
  id: string
  /** 操作文本 */
  text: string
  /** 操作类型 */
  type: 'primary' | 'secondary' | 'danger'
  /** 点击处理函数 */
  handler: () => void
}

/** 确认对话框配置 */
export interface ConfirmDialogConfig {
  /** 是否可见 */
  visible: boolean
  /** 标题 */
  title: string
  /** 消息内容 */
  message: string
  /** 确认按钮文本 */
  confirmText: string
  /** 取消按钮文本 */
  cancelText: string
  /** 对话框类型 */
  type: ConfirmDialogType
  /** 确认回调 */
  onConfirm?: () => void | Promise<void>
  /** 取消回调 */
  onCancel?: () => void
}

/** 模态框状态 */
export interface ModalState {
  /** 模态框名称 */
  name: string
  /** 是否打开 */
  open: boolean
  /** 模态框数据 */
  data?: any
  /** 关闭回调 */
  onClose?: () => void
}

/** 抽屉状态 */
export interface DrawerState {
  /** 抽屉名称 */
  name: string
  /** 是否打开 */
  open: boolean
  /** 抽屉位置 */
  position: 'left' | 'right' | 'top' | 'bottom'
  /** 抽屉数据 */
  data?: any
  /** 关闭回调 */
  onClose?: () => void
}

// ==========================================================================
// 服务层类型 - Service Types
// ==========================================================================

/** 服务响应基础类型 */
export interface ServiceResponse<T = any> {
  /** 是否成功 */
  success: boolean
  /** 数据 */
  data?: T
  /** 错误信息 */
  error?: ServiceError
}

/** 服务错误类型 */
export interface ServiceError {
  /** 错误代码 */
  code: string
  /** 错误信息 */
  message: string
  /** 详细信息 */
  details?: any
  /** 错误堆栈（开发环境） */
  stack?: string
}

/** 存储操作结果 */
export interface StorageResult<T = any> {
  /** 是否成功 */
  success: boolean
  /** 数据 */
  data?: T
  /** 错误信息 */
  error?: string
}

/** 验证结果 */
export interface ValidationResult {
  /** 是否有效 */
  valid: boolean
  /** 错误列表 */
  errors: ValidationError[]
}

/** 表单验证错误 */
export interface ValidationError {
  /** 字段名 */
  field: string
  /** 错误信息 */
  message: string
  /** 错误代码 */
  code?: string
}

/** 表单字段配置 */
export interface FormFieldConfig {
  /** 字段名 */
  name: string
  /** 字段标签 */
  label: string
  /** 字段类型 */
  type: 'text' | 'number' | 'email' | 'password' | 'select' | 'textarea' | 'date' | 'checkbox' | 'radio'
  /** 是否必填 */
  required?: boolean
  /** 占位符 */
  placeholder?: string
  /** 默认值 */
  defaultValue?: any
  /** 验证规则 */
  rules?: ValidationRule[]
  /** 选项（用于select、radio等） */
  options?: FormFieldOption[]
  /** 是否禁用 */
  disabled?: boolean
  /** 帮助文本 */
  helpText?: string
}

/** 表单字段选项 */
export interface FormFieldOption {
  /** 选项值 */
  value: any
  /** 选项标签 */
  label: string
  /** 是否禁用 */
  disabled?: boolean
  /** 图标 */
  icon?: string
}

/** 验证规则 */
export interface ValidationRule {
  /** 规则类型 */
  type: 'required' | 'min' | 'max' | 'minLength' | 'maxLength' | 'pattern' | 'email' | 'phone' | 'url' | 'custom'
  /** 规则值 */
  value?: any
  /** 错误消息 */
  message: string
  /** 自定义验证函数 */
  validator?: (value: any) => boolean | string
}

/** 表单状态 */
export interface FormState<T = Record<string, any>> {
  /** 表单数据 */
  values: T
  /** 字段错误 */
  errors: Record<string, string>
  /** 字段是否被触摸 */
  touched: Record<string, boolean>
  /** 是否正在提交 */
  submitting: boolean
  /** 是否有效 */
  valid: boolean
  /** 是否脏数据 */
  dirty: boolean
}

// ==========================================================================
// 工具类型 - Utility Types
// ==========================================================================

/** API 响应基础类型 */
export interface ApiResponse<T = any> {
  /** 是否成功 */
  success: boolean
  /** 数据 */
  data?: T
  /** 错误信息 */
  message?: string
  /** 错误代码 */
  code?: string
}

/** 异步操作状态 */
export interface AsyncState<T = any> {
  /** 数据 */
  data: T | null
  /** 是否正在加载 */
  loading: boolean
  /** 错误信息 */
  error: string | null
  /** 最后更新时间 */
  lastUpdated: number | null
}

/** 分页查询参数 */
export interface PaginationParams {
  /** 页码（从1开始） */
  page: number
  /** 每页数量 */
  pageSize: number
  /** 排序字段 */
  sortBy?: string
  /** 排序方向 */
  sortOrder?: 'asc' | 'desc'
}

/** 分页查询结果 */
export interface PaginatedResult<T> {
  /** 数据列表 */
  items: T[]
  /** 分页信息 */
  pagination: Pagination
}

/** 操作结果 */
export interface OperationResult<T = any> {
  /** 是否成功 */
  success: boolean
  /** 数据 */
  data?: T
  /** 错误信息 */
  error?: string
  /** 操作时间戳 */
  timestamp: number
}

/** 缓存配置 */
export interface CacheConfig {
  /** 缓存键 */
  key: string
  /** 过期时间（毫秒） */
  ttl: number
  /** 是否启用 */
  enabled: boolean
  /** 最大缓存大小 */
  maxSize?: number
}

/** 缓存项 */
export interface CacheItem<T = any> {
  /** 缓存数据 */
  data: T
  /** 创建时间 */
  createdAt: number
  /** 过期时间 */
  expiresAt: number
  /** 访问次数 */
  accessCount: number
  /** 最后访问时间 */
  lastAccessed: number
}

/** 数据导出配置 */
export interface ExportConfig {
  /** 导出格式 */
  format: ExportFormat
  /** 文件名 */
  filename: string
  /** 日期范围 */
  dateRange?: {
    start: string
    end: string
  }
  /** 包含的字段 */
  fields?: string[]
  /** 筛选条件 */
  filters?: TransactionFilter
}

/** 数据导入结果 */
export interface ImportResult {
  /** 是否成功 */
  success: boolean
  /** 导入的记录数 */
  imported: number
  /** 跳过的记录数 */
  skipped: number
  /** 错误的记录数 */
  errors: number
  /** 错误详情 */
  errorDetails: Array<{
    row: number
    error: string
    data?: any
  }>
  /** 导入时间 */
  timestamp: number
}

/** 备份数据 */
export interface BackupData {
  /** 备份版本 */
  version: string
  /** 备份时间 */
  timestamp: number
  /** 应用版本 */
  appVersion: string
  /** 数据 */
  data: {
    transactions: Transaction[]
    categories: Category[]
    settings: AppSettings
    budgets?: Budget[]
    tags?: Tag[]
  }
  /** 数据校验和 */
  checksum: string
}

/** 搜索结果 */
export interface SearchResult<T = any> {
  /** 结果项 */
  items: T[]
  /** 总数 */
  total: number
  /** 搜索关键词 */
  query: string
  /** 搜索时间 */
  searchTime: number
  /** 建议词 */
  suggestions?: string[]
}

/** 键值对 */
export interface KeyValuePair<K = string, V = any> {
  key: K
  value: V
}

/** 选项项 */
export interface SelectOption<T = any> {
  /** 选项值 */
  value: T
  /** 选项标签 */
  label: string
  /** 是否禁用 */
  disabled?: boolean
  /** 图标 */
  icon?: string
  /** 描述 */
  description?: string
  /** 分组 */
  group?: string
}

// ==========================================================================
// 高级工具类型 - Advanced Utility Types
// ==========================================================================

/** 深度只读 */
export type DeepReadonly<T> = {
  readonly [P in keyof T]: T[P] extends object ? DeepReadonly<T[P]> : T[P]
}

/** 深度可选 */
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P]
}

/** 深度必需 */
export type DeepRequired<T> = {
  [P in keyof T]-?: T[P] extends object ? DeepRequired<T[P]> : T[P]
}

/** 可空类型 */
export type Nullable<T> = T | null

/** 可选可空类型 */
export type Optional<T> = T | null | undefined

/** 非空类型 */
export type NonNullable<T> = T extends null | undefined ? never : T

/** 函数类型 */
export type Func<TArgs extends any[] = any[], TReturn = any> = (...args: TArgs) => TReturn

/** 异步函数类型 */
export type AsyncFunc<TArgs extends any[] = any[], TReturn = any> = (...args: TArgs) => Promise<TReturn>

/** 事件处理函数类型 */
export type EventHandler<T = Event> = (event: T) => void

/** 值或函数类型 */
export type ValueOrFunction<T> = T | (() => T)

/** 值或Promise类型 */
export type ValueOrPromise<T> = T | Promise<T>

/** 提取数组元素类型 */
export type ArrayElement<T> = T extends (infer U)[] ? U : never

/** 提取Promise结果类型 */
export type PromiseResult<T> = T extends Promise<infer U> ? U : T

/** 提取函数参数类型 */
export type FunctionArgs<T> = T extends (...args: infer U) => any ? U : never

/** 提取函数返回类型 */
export type FunctionReturn<T> = T extends (...args: any[]) => infer U ? U : never

/** 字符串字面量联合类型 */
export type StringLiteral<T> = T extends string ? (string extends T ? never : T) : never

/** 数字字面量联合类型 */
export type NumberLiteral<T> = T extends number ? (number extends T ? never : T) : never

/** 对象键类型 */
export type ObjectKeys<T> = keyof T

/** 对象值类型 */
export type ObjectValues<T> = T[keyof T]

/** 对象条目类型 */
export type ObjectEntries<T> = Array<[keyof T, T[keyof T]]>

/** 排除指定键的类型 */
export type ExcludeKeys<T, K extends keyof T> = Omit<T, K>

/** 仅包含指定键的类型 */
export type IncludeKeys<T, K extends keyof T> = Pick<T, K>

/** 可选指定键的类型 */
export type PartialKeys<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>

/** 必需指定键的类型 */
export type RequiredKeys<T, K extends keyof T> = Omit<T, K> & Required<Pick<T, K>>

/** 重命名键类型 */
export type RenameKeys<T, M extends Record<keyof T, string>> = {
  [K in keyof M]: T[K extends keyof T ? K : never]
}

/** 条件类型 */
export type If<C extends boolean, T, F> = C extends true ? T : F

/** 联合类型转交叉类型 */
export type UnionToIntersection<U> = (U extends any ? (k: U) => void : never) extends (k: infer I) => void ? I : never

/** 获取联合类型的最后一个类型 */
export type LastOfUnion<T> = UnionToIntersection<T extends any ? () => T : never> extends () => infer R ? R : never

/** 联合类型转元组类型 */
export type UnionToTuple<T, L = LastOfUnion<T>, N = [T] extends [never] ? true : false> = true extends N
  ? []
  : [...UnionToTuple<Exclude<T, L>>, L]

/** 元组转联合类型 */
export type TupleToUnion<T extends readonly any[]> = T[number]

/** 数组长度类型 */
export type Length<T extends readonly any[]> = T['length']

/** 头部元素类型 */
export type Head<T extends readonly any[]> = T extends readonly [infer H, ...any[]] ? H : never

/** 尾部元素类型 */
export type Tail<T extends readonly any[]> = T extends readonly [any, ...infer R] ? R : never

/** 反转数组类型 */
export type Reverse<T extends readonly any[]> = T extends readonly [...infer Rest, infer Last]
  ? [Last, ...Reverse<Rest>]
  : []

/** 字符串长度类型 */
export type StringLength<S extends string> = S extends `${string}${infer Rest}` ? 1 + StringLength<Rest> : 0

/** 字符串首字母大写 */
export type Capitalize<S extends string> = S extends `${infer F}${infer R}` ? `${Uppercase<F>}${R}` : S

/** 字符串首字母小写 */
export type Uncapitalize<S extends string> = S extends `${infer F}${infer R}` ? `${Lowercase<F>}${R}` : S

/** 驼峰命名转换 */
export type CamelCase<S extends string> = S extends `${infer P1}_${infer P2}${infer P3}`
  ? `${P1}${Uppercase<P2>}${CamelCase<P3>}`
  : S

/** 蛇形命名转换 */
export type SnakeCase<S extends string> = S extends `${infer T}${infer U}`
  ? `${T extends Capitalize<T> ? '_' : ''}${Lowercase<T>}${SnakeCase<U>}`
  : S

/** 烤肉串命名转换 */
export type KebabCase<S extends string> = S extends `${infer T}${infer U}`
  ? `${T extends Capitalize<T> ? '-' : ''}${Lowercase<T>}${KebabCase<U>}`
  : S

// ==========================================================================
// 重新导出其他类型文件 - Re-export Other Type Files
// ==========================================================================

// API 相关类型
export * from './api'

// 组件相关类型
export * from './components'

// 状态管理相关类型
export * from './store'
