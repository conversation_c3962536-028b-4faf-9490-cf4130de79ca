<template>
  <div class="flex flex-col h-screen">
    <!-- 固定标题栏 -->
    <div class="fixed top-0 left-0 right-0 bg-white dark:bg-black border-b border-gray-200 dark:border-gray-800 px-4 py-4 safe-area-top z-30">
      <h1 class="text-lg font-semibold text-center">{{ $t('home.title') }}</h1>
    </div>

    <!-- 可滚动内容区域 -->
    <div class="flex-1 overflow-y-auto pt-16 pb-20 safe-area-top">

    <!-- 头部统计卡片 -->
    <div class="px-4 pt-4 pb-4">
      <BaseCard padding="lg">
        <div class="grid grid-cols-2 gap-4">
          <div class="text-center">
            <p class="text-sm text-gray-500 dark:text-gray-400">{{ $t('home.monthlyIncome') }}</p>
            <p class="text-xl font-semibold text-green-500">
              {{ formatCurrency(totalIncome) }}
            </p>
          </div>
          <div class="text-center">
            <p class="text-sm text-gray-500 dark:text-gray-400">{{ $t('home.monthlyExpense') }}</p>
            <p class="text-xl font-semibold text-red-500">
              {{ formatCurrency(totalExpense) }}
            </p>
          </div>
        </div>
      </BaseCard>
    </div>



    <!-- 最近交易 -->
    <div class="px-4">
      <div class="mb-4">
        <h3 class="text-lg font-semibold">{{ $t('home.recentTransactions') }}</h3>
      </div>

      <div class="space-y-3">
        <BaseCard
          v-for="transaction in recentTransactions"
          :key="transaction.id"
          hover
          @click="showTransactionDetail(transaction)"
        >
          <div class="flex items-center">
            <div class="flex-shrink-0 mr-3">
              <div class="w-10 h-10 flex items-center justify-center">
                <CategoryIcon
                  :icon="getCategoryIcon(transaction.categoryId)"
                  size="w-6 h-6"
                  class="text-gray-600 dark:text-gray-400"
                />
              </div>
            </div>

            <div class="flex-1 min-w-0">
              <div class="flex items-center justify-between">
                <p class="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">
                  {{ getCategoryName(transaction.categoryId) }}
                </p>
                <p class="text-sm font-semibold" :class="[
                  transaction.type === 'income' ? 'text-green-500' : 'text-red-500'
                ]">
                  {{ transaction.type === 'income' ? '+' : '-' }}{{ formatCurrency(transaction.amount) }}
                </p>
              </div>

              <div class="flex items-center justify-between mt-1">
                <p class="text-xs text-gray-500 dark:text-gray-400 truncate">
                  {{ transaction.description || t('home.noNote') }}
                </p>
                <p class="text-xs text-gray-400 dark:text-gray-500">
                  {{ formatDate(transaction.date) }}
                </p>
              </div>
            </div>
          </div>
        </BaseCard>

        <div v-if="recentTransactions.length === 0" class="flex items-center justify-center min-h-[200px]">
          <p class="text-gray-400 dark:text-gray-500">{{ $t('home.noTransactions') }}</p>
        </div>
      </div>
    </div>
    </div>

    <!-- 浮动添加按钮 -->
    <Transition name="fab" appear>
      <button
        v-show="!showAddModal"
        @click="openAddModal('expense')"
        class="fixed bottom-20 right-4 w-14 h-14 bg-blue-500 hover:bg-blue-600 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center z-40 active:scale-95"
      >
        <PlusIcon class="w-7 h-7" />
      </button>
    </Transition>

    <!-- 记账模态窗口 -->
    <Transition name="modal">
      <div
        v-if="showAddModal"
        class="modal-bottom-sheet"
        @click="closeAddModal"
      >
        <div
          class="modal-bottom-content"
          @click.stop
          @touchstart="handleTouchStart"
          @touchmove="handleTouchMove"
          @touchend="handleTouchEnd"
        >
        <!-- 拖拽指示器 -->
        <div class="flex justify-center py-3">
          <div class="w-10 h-1 bg-gray-300 dark:bg-gray-600 rounded-full"></div>
        </div>

        <!-- 收入/支出切换按钮 -->
        <div class="px-4 pb-4">
          <div class="flex bg-gray-100 dark:bg-gray-800 rounded-2xl p-1">
            <button
              @click="modalType = 'expense'"
              class="flex-1 py-2 px-4 rounded-xl text-sm font-medium transition-all duration-200"
              :class="[
                modalType === 'expense'
                  ? 'bg-white dark:bg-black text-red-600 dark:text-red-400 shadow-sm'
                  : 'text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200'
              ]"
            >
              {{ $t('home.expense') }}
            </button>
            <button
              @click="modalType = 'income'"
              class="flex-1 py-2 px-4 rounded-xl text-sm font-medium transition-all duration-200"
              :class="[
                modalType === 'income'
                  ? 'bg-white dark:bg-black text-green-600 dark:text-green-400 shadow-sm'
                  : 'text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200'
              ]"
            >
              {{ $t('home.income') }}
            </button>
          </div>
        </div>

        <!-- 表单选项区域 -->
        <div class="px-4 pb-4 border-b border-gray-200 dark:border-gray-800">
          <!-- 分类选择 -->
          <div class="mb-3">
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              {{ $t('home.category') }}
            </label>
            <div class="grid grid-cols-6 gap-1.5">
              <button
                v-for="category in currentCategories"
                :key="category.id"
                @click="selectCategory(category.id)"
                class="flex flex-col items-center p-1.5 rounded-xl transition-all duration-200"
                :class="[
                  form.categoryId === category.id
                    ? 'bg-blue-500 text-white'
                    : 'bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700'
                ]"
              >
                <div class="mb-0.5">
                  <CategoryIcon
                    :icon="category.icon"
                    size="w-4 h-4"
                    :class="form.categoryId === category.id ? 'text-white' : 'text-gray-600 dark:text-gray-400'"
                  />
                </div>
                <span class="text-xs font-medium">{{ category.name }}</span>
              </button>
            </div>
          </div>

          <!-- 备注和日期按钮 -->
          <div class="grid grid-cols-2 gap-2">
            <button
              @click="showNoteInput = !showNoteInput"
              class="flex items-center justify-between p-3 rounded-xl bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors"
            >
              <span class="text-sm font-medium">{{ form.description || $t('home.note') }}</span>
              <span class="text-xs text-gray-500">{{ form.description ? '✓' : '+' }}</span>
            </button>
            <button
              @click="showDatePicker = !showDatePicker"
              class="flex items-center justify-between p-3 rounded-xl bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors"
            >
              <span class="text-sm font-medium">{{ formatDateDisplay(form.date) }}</span>
              <span class="text-xs text-gray-500">📅</span>
            </button>
          </div>

          <!-- 备注输入框 -->
          <div v-if="showNoteInput" class="mt-2">
            <input
              v-model="form.description"
              :placeholder="$t('home.note') + '...'"
              class="w-full p-3 rounded-xl border border-gray-200 dark:border-gray-700 bg-white dark:bg-black text-sm"
              @blur="showNoteInput = false"
              ref="noteInput"
            />
          </div>

          <!-- 日期选择器 -->
          <div v-if="showDatePicker" class="mt-2">
            <input
              v-model="form.date"
              type="date"
              class="w-full p-3 rounded-xl border border-gray-200 dark:border-gray-700 bg-white dark:bg-black text-sm"
              @blur="showDatePicker = false"
              ref="dateInput"
            />
          </div>
        </div>

        <!-- 数字键盘 -->
        <NumberKeypad
          v-model="form.amount"
          :type="modalType"
          @submit="submitTransaction"
          :submit-disabled="!isFormValid"
        />
        </div>
      </div>
    </Transition>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import {
  PlusCircleIcon,
  MinusCircleIcon,
  XMarkIcon,
  PlusIcon
} from '@heroicons/vue/24/outline'
import { BaseButton, BaseCard, BaseModal, BaseInput, NumberKeypad, CategoryIcon } from '@/components/ui'
import { useTransactionStore } from '@/stores/transactions'
import { useSettingsStore } from '@/stores/settings'
import type { Transaction, TransactionType } from '@/types'

const router = useRouter()
const { t } = useI18n()
const transactionStore = useTransactionStore()
const settingsStore = useSettingsStore()

// Modal state
const showAddModal = ref(false)
const modalType = ref<TransactionType>('expense')
const showNoteInput = ref(false)
const showDatePicker = ref(false)
const noteInput = ref<HTMLInputElement>()
const dateInput = ref<HTMLInputElement>()

// 触摸滑动相关
const touchStartY = ref(0)
const touchCurrentY = ref(0)
const isDragging = ref(false)

// Form state
const form = ref({
  amount: '',
  categoryId: '',
  description: '',
  date: new Date().toISOString().split('T')[0]
})

// Computed properties
const balance = computed(() => transactionStore.balance)
const totalIncome = computed(() => transactionStore.totalIncome)
const totalExpense = computed(() => transactionStore.totalExpense)
const recentTransactions = computed(() => transactionStore.recentTransactions)

// Modal computed properties
const currentCategories = computed(() => {
  return modalType.value === 'expense'
    ? transactionStore.expenseCategories
    : transactionStore.incomeCategories
})

const isFormValid = computed(() => {
  return form.value.amount &&
         parseFloat(form.value.amount) > 0 &&
         form.value.categoryId &&
         form.value.date
})

// Methods
function formatCurrency(amount: number): string {
  return settingsStore.formatCurrency(amount)
}

function formatDate(dateString: string): string {
  const date = new Date(dateString)
  const now = new Date()
  const diffTime = now.getTime() - date.getTime()
  const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24))

  if (diffDays === 0) {
    return t('home.today')
  } else if (diffDays === 1) {
    return t('home.yesterday')
  } else if (diffDays < 7) {
    return `${diffDays}${t('home.daysAgo')}`
  } else {
    return date.toLocaleDateString(undefined, { month: 'short', day: 'numeric' })
  }
}

function getCategoryIcon(categoryId: string): string {
  const category = transactionStore.getCategoryById(categoryId)
  return category?.icon || '📝'
}

function getCategoryName(categoryId: string): string {
  const category = transactionStore.getCategoryById(categoryId)
  return category?.name || t('stats.unknownCategory')
}

function getCategoryColor(categoryId: string): string {
  const category = transactionStore.getCategoryById(categoryId)
  return category?.color || '#6b7280'
}

function showTransactionDetail(transaction: Transaction) {
  router.push(`/transaction/${transaction.id}`)
}

// Modal methods
function openAddModal(type: TransactionType) {
  modalType.value = type
  form.value = {
    amount: '',
    categoryId: '',
    description: '',
    date: new Date().toISOString().split('T')[0]
  }
  showAddModal.value = true
}

function closeAddModal() {
  showAddModal.value = false
}

function selectCategory(categoryId: string) {
  form.value.categoryId = categoryId
}

function submitTransaction() {
  if (!isFormValid.value) return

  const amount = Math.round(parseFloat(form.value.amount) * 100) // 转换为分

  transactionStore.addTransaction({
    type: modalType.value,
    amount: amount,
    categoryId: form.value.categoryId,
    description: form.value.description || '',
    date: form.value.date
  })

  closeAddModal()
}

// 触摸事件处理
function handleTouchStart(e: TouchEvent) {
  touchStartY.value = e.touches[0].clientY
  isDragging.value = false
}

function handleTouchMove(e: TouchEvent) {
  if (!isDragging.value) {
    isDragging.value = true
  }
  touchCurrentY.value = e.touches[0].clientY
}

function handleTouchEnd() {
  if (isDragging.value) {
    const deltaY = touchCurrentY.value - touchStartY.value
    // 如果向下滑动超过100px，关闭模态框
    if (deltaY > 100) {
      closeAddModal()
    }
  }
  isDragging.value = false
}

// 日期格式化显示
function formatDateDisplay(dateStr: string) {
  if (!dateStr) return t('home.date')
  const date = new Date(dateStr)
  const today = new Date()
  const yesterday = new Date(today)
  yesterday.setDate(yesterday.getDate() - 1)

  if (date.toDateString() === today.toDateString()) {
    return t('home.today')
  } else if (date.toDateString() === yesterday.toDateString()) {
    return t('home.yesterday')
  } else {
    return `${date.getMonth() + 1}/${date.getDate()}`
  }
}

// 监听输入框显示状态，自动聚焦
watch(showNoteInput, (show) => {
  if (show) {
    nextTick(() => {
      noteInput.value?.focus()
    })
  }
})

watch(showDatePicker, (show) => {
  if (show) {
    nextTick(() => {
      dateInput.value?.focus()
    })
  }
})
</script>

<style scoped>
/* 浮动按钮动画 */
.fab-enter-active,
.fab-leave-active {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.fab-enter-from {
  opacity: 0;
  transform: scale(0) rotate(-180deg);
}

.fab-leave-to {
  opacity: 0;
  transform: scale(0) rotate(180deg);
}

/* 模态窗口动画优化 */
.modal-enter-active {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.modal-leave-active {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.modal-enter-from {
  opacity: 0;
  transform: translateY(100%);
}

.modal-leave-to {
  opacity: 0;
  transform: translateY(100%);
}
</style>
