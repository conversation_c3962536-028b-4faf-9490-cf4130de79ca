import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { transactionService, categoryService } from '@/services'
import type {
  Transaction,
  Category,
  TransactionType,
  MonthlyStats,
  CategoryStats,
  TransactionFilter,
  AsyncState
} from '@/types'

/**
 * 交易状态管理
 * 使用服务层进行数据操作，提供响应式状态和计算属性
 */
export const useTransactionStore = defineStore('transactions', () => {
  // ==========================================================================
  // 状态定义 - State
  // ==========================================================================

  /** 交易数据状态 */
  const transactionsState = ref<AsyncState<Transaction[]>>({
    data: null,
    loading: false,
    error: null,
    lastUpdated: null
  })

  /** 分类数据状态 */
  const categoriesState = ref<AsyncState<Category[]>>({
    data: null,
    loading: false,
    error: null,
    lastUpdated: null
  })

  /** 当前筛选条件 */
  const currentFilter = ref<TransactionFilter>({})

  // ==========================================================================
  // 计算属性 - Computed
  // ==========================================================================

  /** 所有交易记录 */
  const transactions = computed(() => transactionsState.value.data || [])

  /** 所有分类 */
  const categories = computed(() => categoriesState.value.data || [])

  /** 支出分类 */
  const expenseCategories = computed(() =>
    categories.value.filter(cat => cat.type === 'expense')
  )

  /** 收入分类 */
  const incomeCategories = computed(() =>
    categories.value.filter(cat => cat.type === 'income')
  )

  /** 按日期排序的交易记录 */
  const sortedTransactions = computed(() =>
    [...transactions.value].sort((a, b) =>
      new Date(b.date).getTime() - new Date(a.date).getTime()
    )
  )

  /** 最近的交易记录 */
  const recentTransactions = computed(() =>
    sortedTransactions.value.slice(0, 10)
  )

  /** 总收入 */
  const totalIncome = computed(() =>
    transactions.value
      .filter(t => t.type === 'income')
      .reduce((sum, t) => sum + t.amount, 0)
  )

  /** 总支出 */
  const totalExpense = computed(() =>
    transactions.value
      .filter(t => t.type === 'expense')
      .reduce((sum, t) => sum + t.amount, 0)
  )

  /** 余额 */
  const balance = computed(() => totalIncome.value - totalExpense.value)

  /** 是否正在加载 */
  const isLoading = computed(() =>
    transactionsState.value.loading || categoriesState.value.loading
  )

  /** 是否有错误 */
  const hasError = computed(() =>
    !!transactionsState.value.error || !!categoriesState.value.error
  )

  /** 错误信息 */
  const errorMessage = computed(() =>
    transactionsState.value.error || categoriesState.value.error
  )

  // ==========================================================================
  // 交易操作方法 - Transaction Actions
  // ==========================================================================

  /** 加载所有交易记录 */
  async function loadTransactions(forceRefresh: boolean = false) {
    if (transactionsState.value.loading) return

    transactionsState.value.loading = true
    transactionsState.value.error = null

    try {
      const result = await transactionService.getAllTransactions(forceRefresh)

      if (result.success && result.data) {
        transactionsState.value.data = result.data
        transactionsState.value.lastUpdated = Date.now()
      } else {
        throw new Error(result.error?.message || 'Failed to load transactions')
      }
    } catch (error) {
      transactionsState.value.error = error instanceof Error ? error.message : 'Unknown error'
      console.error('Failed to load transactions:', error)
    } finally {
      transactionsState.value.loading = false
    }
  }

  /** 创建交易记录 */
  async function createTransaction(
    transactionData: Omit<Transaction, 'id' | 'createdAt' | 'updatedAt'>
  ): Promise<Transaction | null> {
    try {
      const result = await transactionService.createTransaction(transactionData)

      if (result.success && result.data) {
        // 更新本地状态
        if (transactionsState.value.data) {
          transactionsState.value.data.push(result.data)
        }
        return result.data
      } else {
        throw new Error(result.error?.message || 'Failed to create transaction')
      }
    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : 'Unknown error'
      transactionsState.value.error = errorMsg
      console.error('Failed to create transaction:', error)
      return null
    }
  }

  /** 更新交易记录 */
  async function updateTransaction(
    id: string,
    updates: Partial<Omit<Transaction, 'id' | 'createdAt'>>
  ): Promise<boolean> {
    try {
      const result = await transactionService.updateTransaction(id, updates)

      if (result.success && result.data) {
        // 更新本地状态
        if (transactionsState.value.data) {
          const index = transactionsState.value.data.findIndex(t => t.id === id)
          if (index !== -1) {
            transactionsState.value.data[index] = result.data
          }
        }
        return true
      } else {
        throw new Error(result.error?.message || 'Failed to update transaction')
      }
    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : 'Unknown error'
      transactionsState.value.error = errorMsg
      console.error('Failed to update transaction:', error)
      return false
    }
  }

  /** 删除交易记录 */
  async function deleteTransaction(id: string): Promise<boolean> {
    try {
      const result = await transactionService.deleteTransaction(id)

      if (result.success) {
        // 更新本地状态
        if (transactionsState.value.data) {
          transactionsState.value.data = transactionsState.value.data.filter(t => t.id !== id)
        }
        return true
      } else {
        throw new Error(result.error?.message || 'Failed to delete transaction')
      }
    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : 'Unknown error'
      transactionsState.value.error = errorMsg
      console.error('Failed to delete transaction:', error)
      return false
    }
  }

  /** 批量删除交易记录 */
  async function deleteTransactions(ids: string[]): Promise<number> {
    try {
      const result = await transactionService.deleteTransactions(ids)

      if (result.success && typeof result.data === 'number') {
        // 更新本地状态
        if (transactionsState.value.data) {
          transactionsState.value.data = transactionsState.value.data.filter(t => !ids.includes(t.id))
        }
        return result.data
      } else {
        throw new Error(result.error?.message || 'Failed to delete transactions')
      }
    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : 'Unknown error'
      transactionsState.value.error = errorMsg
      console.error('Failed to delete transactions:', error)
      return 0
    }
  }

  // ==========================================================================
  // 分类操作方法 - Category Actions
  // ==========================================================================

  /** 加载所有分类 */
  async function loadCategories(forceRefresh: boolean = false) {
    if (categoriesState.value.loading) return

    categoriesState.value.loading = true
    categoriesState.value.error = null

    try {
      const result = await categoryService.getAllCategories(forceRefresh)

      if (result.success && result.data) {
        categoriesState.value.data = result.data
        categoriesState.value.lastUpdated = Date.now()
      } else {
        throw new Error(result.error?.message || 'Failed to load categories')
      }
    } catch (error) {
      categoriesState.value.error = error instanceof Error ? error.message : 'Unknown error'
      console.error('Failed to load categories:', error)
    } finally {
      categoriesState.value.loading = false
    }
  }

  /** 创建分类 */
  async function createCategory(
    categoryData: Omit<Category, 'id'>
  ): Promise<Category | null> {
    try {
      const result = await categoryService.createCategory(categoryData)

      if (result.success && result.data) {
        // 更新本地状态
        if (categoriesState.value.data) {
          categoriesState.value.data.push(result.data)
        }
        return result.data
      } else {
        throw new Error(result.error?.message || 'Failed to create category')
      }
    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : 'Unknown error'
      categoriesState.value.error = errorMsg
      console.error('Failed to create category:', error)
      return null
    }
  }

  /** 更新分类 */
  async function updateCategory(
    id: string,
    updates: Partial<Omit<Category, 'id'>>
  ): Promise<boolean> {
    try {
      const result = await categoryService.updateCategory(id, updates)

      if (result.success && result.data) {
        // 更新本地状态
        if (categoriesState.value.data) {
          const index = categoriesState.value.data.findIndex(c => c.id === id)
          if (index !== -1) {
            categoriesState.value.data[index] = result.data
          }
        }
        return true
      } else {
        throw new Error(result.error?.message || 'Failed to update category')
      }
    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : 'Unknown error'
      categoriesState.value.error = errorMsg
      console.error('Failed to update category:', error)
      return false
    }
  }

  /** 删除分类 */
  async function deleteCategory(id: string): Promise<boolean> {
    try {
      // 检查是否有交易使用此分类
      const hasTransactions = transactions.value.some(t => t.categoryId === id)
      if (hasTransactions) {
        throw new Error('Cannot delete category that has transactions')
      }

      const result = await categoryService.deleteCategory(id)

      if (result.success) {
        // 更新本地状态
        if (categoriesState.value.data) {
          categoriesState.value.data = categoriesState.value.data.filter(c => c.id !== id)
        }
        return true
      } else {
        throw new Error(result.error?.message || 'Failed to delete category')
      }
    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : 'Unknown error'
      categoriesState.value.error = errorMsg
      console.error('Failed to delete category:', error)
      return false
    }
  }

  // ==========================================================================
  // 查询和统计方法 - Query and Statistics Methods
  // ==========================================================================

  /** 根据ID获取分类 */
  function getCategoryById(id: string): Category | undefined {
    return categories.value.find(c => c.id === id)
  }

  /** 根据ID获取交易记录 */
  function getTransactionById(id: string): Transaction | undefined {
    return transactions.value.find(t => t.id === id)
  }

  /** 获取指定月份的交易记录 */
  function getTransactionsByMonth(monthString: string): Transaction[] {
    return transactions.value.filter(t => t.date.substring(0, 7) === monthString)
  }

  /** 获取指定分类的交易记录 */
  function getTransactionsByCategory(categoryId: string): Transaction[] {
    return transactions.value.filter(t => t.categoryId === categoryId)
  }

  /** 根据筛选条件获取交易记录 */
  function getFilteredTransactions(filter: TransactionFilter): Transaction[] {
    return transactions.value.filter(transaction => {
      // 类型筛选
      if (filter.type && transaction.type !== filter.type) {
        return false
      }

      // 分类筛选
      if (filter.categoryIds && filter.categoryIds.length > 0) {
        if (!filter.categoryIds.includes(transaction.categoryId)) {
          return false
        }
      }

      // 日期范围筛选
      if (filter.startDate && transaction.date < filter.startDate) {
        return false
      }
      if (filter.endDate && transaction.date > filter.endDate) {
        return false
      }

      // 金额范围筛选
      if (filter.minAmount !== undefined && transaction.amount < filter.minAmount) {
        return false
      }
      if (filter.maxAmount !== undefined && transaction.amount > filter.maxAmount) {
        return false
      }

      // 关键词搜索
      if (filter.keyword) {
        const keyword = filter.keyword.toLowerCase()
        const category = getCategoryById(transaction.categoryId)
        const searchText = `${transaction.description} ${category?.name || ''}`.toLowerCase()
        if (!searchText.includes(keyword)) {
          return false
        }
      }

      return true
    })
  }

  /** 获取月度统计 */
  function getMonthlyStats(monthString: string): MonthlyStats {
    const monthTransactions = getTransactionsByMonth(monthString)

    const totalIncome = monthTransactions
      .filter(t => t.type === 'income')
      .reduce((sum, t) => sum + t.amount, 0)

    const totalExpense = monthTransactions
      .filter(t => t.type === 'expense')
      .reduce((sum, t) => sum + t.amount, 0)

    return {
      month: monthString,
      totalIncome,
      totalExpense,
      balance: totalIncome - totalExpense,
      transactionCount: monthTransactions.length
    }
  }

  /** 获取分类统计 */
  function getCategoryStats(type: TransactionType, monthString?: string): CategoryStats[] {
    let targetTransactions = transactions.value.filter(t => t.type === type)

    if (monthString) {
      targetTransactions = targetTransactions.filter(t => t.date.substring(0, 7) === monthString)
    }

    const total = targetTransactions.reduce((sum, t) => sum + t.amount, 0)

    // 按分类分组统计
    const categoryMap = new Map<string, { amount: number; count: number }>()

    targetTransactions.forEach(transaction => {
      const existing = categoryMap.get(transaction.categoryId) || { amount: 0, count: 0 }
      categoryMap.set(transaction.categoryId, {
        amount: existing.amount + transaction.amount,
        count: existing.count + 1
      })
    })

    // 转换为统计数组
    const stats: CategoryStats[] = []
    categoryMap.forEach((data, categoryId) => {
      const category = getCategoryById(categoryId)
      if (category) {
        stats.push({
          categoryId,
          categoryName: category.name,
          categoryIcon: category.icon,
          categoryColor: category.color,
          amount: data.amount,
          percentage: total > 0 ? (data.amount / total) * 100 : 0,
          count: data.count
        })
      }
    })

    return stats.sort((a, b) => b.amount - a.amount)
  }

  // ==========================================================================
  // 数据管理方法 - Data Management Methods
  // ==========================================================================

  /** 初始化数据 */
  async function initialize() {
    await Promise.all([
      loadTransactions(),
      loadCategories()
    ])
  }

  /** 刷新所有数据 */
  async function refreshAll() {
    await Promise.all([
      loadTransactions(true),
      loadCategories(true)
    ])
  }

  /** 清除错误状态 */
  function clearErrors() {
    transactionsState.value.error = null
    categoriesState.value.error = null
  }

  /** 设置筛选条件 */
  function setFilter(filter: TransactionFilter) {
    currentFilter.value = { ...filter }
  }

  /** 清除筛选条件 */
  function clearFilter() {
    currentFilter.value = {}
  }

  // ==========================================================================
  // 返回接口 - Return Interface
  // ==========================================================================

  return {
    // 响应式状态
    transactions,
    categories,
    isLoading,
    hasError,
    errorMessage,
    currentFilter,

    // 计算属性
    expenseCategories,
    incomeCategories,
    sortedTransactions,
    recentTransactions,
    totalIncome,
    totalExpense,
    balance,

    // 数据加载方法
    initialize,
    loadTransactions,
    loadCategories,
    refreshAll,

    // 交易操作方法
    createTransaction,
    addTransaction: createTransaction, // 别名，为了向后兼容
    updateTransaction,
    deleteTransaction,
    deleteTransactions,

    // 分类操作方法
    createCategory,
    updateCategory,
    deleteCategory,

    // 查询方法
    getCategoryById,
    getTransactionById,
    getTransactionsByMonth,
    getTransactionsByCategory,
    getFilteredTransactions,
    getMonthlyStats,
    getCategoryStats,

    // 状态管理方法
    clearErrors,
    setFilter,
    clearFilter
  }
})
