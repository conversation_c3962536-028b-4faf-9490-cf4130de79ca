import { ref, computed, nextTick } from 'vue'

/**
 * UI状态管理组合式函数
 * 提供通用的UI状态和交互方法
 */
export function useUI() {
  // ==========================================================================
  // 响应式状态 - Reactive State
  // ==========================================================================

  /** 全局加载状态 */
  const globalLoading = ref(false)

  /** 模态框状态 */
  const modals = ref<Record<string, boolean>>({})

  /** 抽屉状态 */
  const drawers = ref<Record<string, boolean>>({})

  /** 下拉菜单状态 */
  const dropdowns = ref<Record<string, boolean>>({})

  /** 工具提示状态 */
  const tooltips = ref<Record<string, boolean>>({})

  /** 通知列表 */
  const notifications = ref<Array<{
    id: string
    type: 'success' | 'error' | 'warning' | 'info'
    title: string
    message?: string
    duration?: number
    persistent?: boolean
    createdAt: number
  }>>([])

  /** 确认对话框状态 */
  const confirmDialog = ref<{
    visible: boolean
    title: string
    message: string
    confirmText: string
    cancelText: string
    type: 'info' | 'warning' | 'error'
    onConfirm?: () => void | Promise<void>
    onCancel?: () => void
  }>({
    visible: false,
    title: '',
    message: '',
    confirmText: '确认',
    cancelText: '取消',
    type: 'info'
  })

  // ==========================================================================
  // 计算属性 - Computed Properties
  // ==========================================================================

  /** 是否有任何模态框打开 */
  const hasOpenModal = computed(() => 
    Object.values(modals.value).some(Boolean)
  )

  /** 是否有任何抽屉打开 */
  const hasOpenDrawer = computed(() => 
    Object.values(drawers.value).some(Boolean)
  )

  /** 是否有任何下拉菜单打开 */
  const hasOpenDropdown = computed(() => 
    Object.values(dropdowns.value).some(Boolean)
  )

  /** 未读通知数量 */
  const unreadNotificationCount = computed(() => 
    notifications.value.length
  )

  // ==========================================================================
  // 加载状态管理 - Loading State Management
  // ==========================================================================

  /** 设置全局加载状态 */
  function setGlobalLoading(loading: boolean) {
    globalLoading.value = loading
  }

  /** 显示全局加载 */
  function showGlobalLoading() {
    setGlobalLoading(true)
  }

  /** 隐藏全局加载 */
  function hideGlobalLoading() {
    setGlobalLoading(false)
  }

  /** 异步操作包装器 */
  async function withLoading<T>(
    operation: () => Promise<T>,
    showGlobal: boolean = false
  ): Promise<T> {
    if (showGlobal) showGlobalLoading()
    
    try {
      return await operation()
    } finally {
      if (showGlobal) hideGlobalLoading()
    }
  }

  // ==========================================================================
  // 模态框管理 - Modal Management
  // ==========================================================================

  /** 打开模态框 */
  function openModal(name: string) {
    modals.value[name] = true
  }

  /** 关闭模态框 */
  function closeModal(name: string) {
    modals.value[name] = false
  }

  /** 切换模态框状态 */
  function toggleModal(name: string) {
    modals.value[name] = !modals.value[name]
  }

  /** 检查模态框是否打开 */
  function isModalOpen(name: string): boolean {
    return modals.value[name] || false
  }

  /** 关闭所有模态框 */
  function closeAllModals() {
    Object.keys(modals.value).forEach(name => {
      modals.value[name] = false
    })
  }

  // ==========================================================================
  // 抽屉管理 - Drawer Management
  // ==========================================================================

  /** 打开抽屉 */
  function openDrawer(name: string) {
    drawers.value[name] = true
  }

  /** 关闭抽屉 */
  function closeDrawer(name: string) {
    drawers.value[name] = false
  }

  /** 切换抽屉状态 */
  function toggleDrawer(name: string) {
    drawers.value[name] = !drawers.value[name]
  }

  /** 检查抽屉是否打开 */
  function isDrawerOpen(name: string): boolean {
    return drawers.value[name] || false
  }

  /** 关闭所有抽屉 */
  function closeAllDrawers() {
    Object.keys(drawers.value).forEach(name => {
      drawers.value[name] = false
    })
  }

  // ==========================================================================
  // 下拉菜单管理 - Dropdown Management
  // ==========================================================================

  /** 打开下拉菜单 */
  function openDropdown(name: string) {
    // 关闭其他下拉菜单
    Object.keys(dropdowns.value).forEach(key => {
      if (key !== name) dropdowns.value[key] = false
    })
    dropdowns.value[name] = true
  }

  /** 关闭下拉菜单 */
  function closeDropdown(name: string) {
    dropdowns.value[name] = false
  }

  /** 切换下拉菜单状态 */
  function toggleDropdown(name: string) {
    if (dropdowns.value[name]) {
      closeDropdown(name)
    } else {
      openDropdown(name)
    }
  }

  /** 检查下拉菜单是否打开 */
  function isDropdownOpen(name: string): boolean {
    return dropdowns.value[name] || false
  }

  /** 关闭所有下拉菜单 */
  function closeAllDropdowns() {
    Object.keys(dropdowns.value).forEach(name => {
      dropdowns.value[name] = false
    })
  }

  // ==========================================================================
  // 通知管理 - Notification Management
  // ==========================================================================

  /** 生成通知ID */
  function generateNotificationId(): string {
    return `notification_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  /** 显示通知 */
  function showNotification(options: {
    type: 'success' | 'error' | 'warning' | 'info'
    title: string
    message?: string
    duration?: number
    persistent?: boolean
  }) {
    const notification = {
      id: generateNotificationId(),
      type: options.type,
      title: options.title,
      message: options.message,
      duration: options.duration ?? 5000,
      persistent: options.persistent ?? false,
      createdAt: Date.now()
    }

    notifications.value.push(notification)

    // 自动移除非持久化通知
    if (!notification.persistent && notification.duration > 0) {
      setTimeout(() => {
        removeNotification(notification.id)
      }, notification.duration)
    }

    return notification.id
  }

  /** 显示成功通知 */
  function showSuccess(title: string, message?: string, duration?: number) {
    return showNotification({ type: 'success', title, message, duration })
  }

  /** 显示错误通知 */
  function showError(title: string, message?: string, persistent?: boolean) {
    return showNotification({ type: 'error', title, message, persistent })
  }

  /** 显示警告通知 */
  function showWarning(title: string, message?: string, duration?: number) {
    return showNotification({ type: 'warning', title, message, duration })
  }

  /** 显示信息通知 */
  function showInfo(title: string, message?: string, duration?: number) {
    return showNotification({ type: 'info', title, message, duration })
  }

  /** 移除通知 */
  function removeNotification(id: string) {
    const index = notifications.value.findIndex(n => n.id === id)
    if (index !== -1) {
      notifications.value.splice(index, 1)
    }
  }

  /** 清除所有通知 */
  function clearAllNotifications() {
    notifications.value = []
  }

  // ==========================================================================
  // 确认对话框管理 - Confirm Dialog Management
  // ==========================================================================

  /** 显示确认对话框 */
  function showConfirm(options: {
    title: string
    message: string
    confirmText?: string
    cancelText?: string
    type?: 'info' | 'warning' | 'error'
  }): Promise<boolean> {
    return new Promise((resolve) => {
      confirmDialog.value = {
        visible: true,
        title: options.title,
        message: options.message,
        confirmText: options.confirmText || '确认',
        cancelText: options.cancelText || '取消',
        type: options.type || 'info',
        onConfirm: () => {
          confirmDialog.value.visible = false
          resolve(true)
        },
        onCancel: () => {
          confirmDialog.value.visible = false
          resolve(false)
        }
      }
    })
  }

  /** 隐藏确认对话框 */
  function hideConfirm() {
    confirmDialog.value.visible = false
  }

  // ==========================================================================
  // 工具方法 - Utility Methods
  // ==========================================================================

  /** 滚动到顶部 */
  function scrollToTop(smooth: boolean = true) {
    window.scrollTo({
      top: 0,
      behavior: smooth ? 'smooth' : 'auto'
    })
  }

  /** 滚动到元素 */
  function scrollToElement(element: HTMLElement | string, smooth: boolean = true) {
    const target = typeof element === 'string' 
      ? document.querySelector(element) as HTMLElement
      : element

    if (target) {
      target.scrollIntoView({
        behavior: smooth ? 'smooth' : 'auto',
        block: 'start'
      })
    }
  }

  /** 复制到剪贴板 */
  async function copyToClipboard(text: string): Promise<boolean> {
    try {
      await navigator.clipboard.writeText(text)
      showSuccess('复制成功', '内容已复制到剪贴板')
      return true
    } catch (error) {
      showError('复制失败', '无法访问剪贴板')
      return false
    }
  }

  /** 防抖函数 */
  function debounce<T extends (...args: any[]) => any>(
    func: T,
    delay: number
  ): (...args: Parameters<T>) => void {
    let timeoutId: ReturnType<typeof setTimeout>
    
    return (...args: Parameters<T>) => {
      clearTimeout(timeoutId)
      timeoutId = setTimeout(() => func(...args), delay)
    }
  }

  /** 节流函数 */
  function throttle<T extends (...args: any[]) => any>(
    func: T,
    delay: number
  ): (...args: Parameters<T>) => void {
    let lastCall = 0
    
    return (...args: Parameters<T>) => {
      const now = Date.now()
      if (now - lastCall >= delay) {
        lastCall = now
        func(...args)
      }
    }
  }

  // ==========================================================================
  // 返回接口 - Return Interface
  // ==========================================================================

  return {
    // 响应式状态
    globalLoading,
    notifications,
    confirmDialog,
    hasOpenModal,
    hasOpenDrawer,
    hasOpenDropdown,
    unreadNotificationCount,

    // 加载状态管理
    setGlobalLoading,
    showGlobalLoading,
    hideGlobalLoading,
    withLoading,

    // 模态框管理
    openModal,
    closeModal,
    toggleModal,
    isModalOpen,
    closeAllModals,

    // 抽屉管理
    openDrawer,
    closeDrawer,
    toggleDrawer,
    isDrawerOpen,
    closeAllDrawers,

    // 下拉菜单管理
    openDropdown,
    closeDropdown,
    toggleDropdown,
    isDropdownOpen,
    closeAllDropdowns,

    // 通知管理
    showNotification,
    showSuccess,
    showError,
    showWarning,
    showInfo,
    removeNotification,
    clearAllNotifications,

    // 确认对话框管理
    showConfirm,
    hideConfirm,

    // 工具方法
    scrollToTop,
    scrollToElement,
    copyToClipboard,
    debounce,
    throttle
  }
}
