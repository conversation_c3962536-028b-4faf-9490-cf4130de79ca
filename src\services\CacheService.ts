/**
 * 缓存服务
 * Cache service for performance optimization
 */

import { BaseService } from './base/BaseService'
import type { ServiceResponse } from '@/types'

/**
 * 缓存项接口
 */
export interface CacheItem<T = any> {
  /** 缓存值 */
  value: T
  /** 过期时间戳 */
  expiry: number
  /** 创建时间戳 */
  created: number
  /** 访问次数 */
  accessCount: number
  /** 最后访问时间 */
  lastAccessed: number
  /** 缓存大小（字节） */
  size?: number
}

/**
 * 缓存配置接口
 */
export interface CacheConfig {
  /** 默认过期时间（毫秒） */
  defaultTTL: number
  /** 最大缓存项数量 */
  maxItems: number
  /** 最大缓存大小（字节） */
  maxSize: number
  /** 清理间隔（毫秒） */
  cleanupInterval: number
  /** 是否启用统计 */
  enableStats: boolean
  /** 存储类型 */
  storage: 'memory' | 'localStorage' | 'sessionStorage'
}

/**
 * 缓存统计接口
 */
export interface CacheStats {
  /** 命中次数 */
  hits: number
  /** 未命中次数 */
  misses: number
  /** 命中率 */
  hitRate: number
  /** 总项数 */
  itemCount: number
  /** 总大小 */
  totalSize: number
  /** 过期项数 */
  expiredItems: number
}

/**
 * 缓存策略枚举
 */
export enum CacheStrategy {
  /** 最近最少使用 */
  LRU = 'lru',
  /** 先进先出 */
  FIFO = 'fifo',
  /** 最少使用 */
  LFU = 'lfu',
  /** 随机替换 */
  RANDOM = 'random'
}

/**
 * 缓存服务类
 */
export class CacheService extends BaseService {
  private static instance: CacheService
  private cache: Map<string, CacheItem> = new Map()
  private config: CacheConfig
  private stats: CacheStats
  private cleanupTimer?: number

  private constructor() {
    super()
    this.config = {
      defaultTTL: 5 * 60 * 1000, // 5分钟
      maxItems: 1000,
      maxSize: 10 * 1024 * 1024, // 10MB
      cleanupInterval: 60 * 1000, // 1分钟
      enableStats: true,
      storage: 'memory'
    }
    this.stats = {
      hits: 0,
      misses: 0,
      hitRate: 0,
      itemCount: 0,
      totalSize: 0,
      expiredItems: 0
    }
  }

  /**
   * 获取单例实例
   */
  static getInstance(): CacheService {
    if (!CacheService.instance) {
      CacheService.instance = new CacheService()
    }
    return CacheService.instance
  }

  /**
   * 初始化缓存服务
   */
  async initialize(config?: Partial<CacheConfig>): Promise<ServiceResponse<void>> {
    try {
      if (config) {
        this.config = { ...this.config, ...config }
      }

      // 从持久化存储加载缓存
      if (this.config.storage !== 'memory') {
        await this.loadFromStorage()
      }

      // 启动清理定时器
      this.startCleanupTimer()

      return this.success(undefined)
    } catch (error) {
      return this.handleError(error, 'CACHE_INIT_ERROR')
    }
  }

  /**
   * 设置缓存项
   */
  async set<T>(
    key: string, 
    value: T, 
    ttl?: number
  ): Promise<ServiceResponse<void>> {
    try {
      const expiry = Date.now() + (ttl || this.config.defaultTTL)
      const size = this.calculateSize(value)
      
      const item: CacheItem<T> = {
        value,
        expiry,
        created: Date.now(),
        accessCount: 0,
        lastAccessed: Date.now(),
        size
      }

      // 检查是否需要清理空间
      await this.ensureSpace(size)

      this.cache.set(key, item)
      this.updateStats()

      // 持久化到存储
      if (this.config.storage !== 'memory') {
        await this.saveToStorage()
      }

      return this.success(undefined)
    } catch (error) {
      return this.handleError(error, 'CACHE_SET_ERROR')
    }
  }

  /**
   * 获取缓存项
   */
  async get<T>(key: string): Promise<ServiceResponse<T | null>> {
    try {
      const item = this.cache.get(key) as CacheItem<T> | undefined

      if (!item) {
        this.recordMiss()
        return this.success(null)
      }

      // 检查是否过期
      if (Date.now() > item.expiry) {
        this.cache.delete(key)
        this.recordMiss()
        this.stats.expiredItems++
        return this.success(null)
      }

      // 更新访问信息
      item.accessCount++
      item.lastAccessed = Date.now()
      
      this.recordHit()
      return this.success(item.value)
    } catch (error) {
      return this.handleError(error, 'CACHE_GET_ERROR')
    }
  }

  /**
   * 删除缓存项
   */
  async delete(key: string): Promise<ServiceResponse<boolean>> {
    try {
      const deleted = this.cache.delete(key)
      this.updateStats()

      if (this.config.storage !== 'memory') {
        await this.saveToStorage()
      }

      return this.success(deleted)
    } catch (error) {
      return this.handleError(error, 'CACHE_DELETE_ERROR')
    }
  }

  /**
   * 检查缓存项是否存在
   */
  async has(key: string): Promise<ServiceResponse<boolean>> {
    try {
      const item = this.cache.get(key)
      
      if (!item) {
        return this.success(false)
      }

      // 检查是否过期
      if (Date.now() > item.expiry) {
        this.cache.delete(key)
        this.stats.expiredItems++
        return this.success(false)
      }

      return this.success(true)
    } catch (error) {
      return this.handleError(error, 'CACHE_HAS_ERROR')
    }
  }

  /**
   * 清空缓存
   */
  async clear(): Promise<ServiceResponse<void>> {
    try {
      this.cache.clear()
      this.resetStats()

      if (this.config.storage !== 'memory') {
        await this.saveToStorage()
      }

      return this.success(undefined)
    } catch (error) {
      return this.handleError(error, 'CACHE_CLEAR_ERROR')
    }
  }

  /**
   * 获取缓存大小
   */
  size(): number {
    return this.cache.size
  }

  /**
   * 获取所有缓存键
   */
  keys(): string[] {
    return Array.from(this.cache.keys())
  }

  /**
   * 获取缓存统计
   */
  getStats(): CacheStats {
    this.updateStats()
    return { ...this.stats }
  }

  /**
   * 重置统计
   */
  resetStats(): void {
    this.stats = {
      hits: 0,
      misses: 0,
      hitRate: 0,
      itemCount: 0,
      totalSize: 0,
      expiredItems: 0
    }
  }

  /**
   * 缓存装饰器
   */
  cached<T extends any[], R>(
    key: string | ((...args: T) => string),
    ttl?: number
  ) {
    return (target: any, propertyName: string, descriptor: PropertyDescriptor) => {
      const method = descriptor.value

      descriptor.value = async (...args: T): Promise<R> => {
        const cacheKey = typeof key === 'function' ? key(...args) : key
        
        // 尝试从缓存获取
        const cached = await this.get<R>(cacheKey)
        if (cached.success && cached.data !== null) {
          return cached.data
        }

        // 执行原方法
        const result = await method.apply(target, args)
        
        // 缓存结果
        await this.set(cacheKey, result, ttl)
        
        return result
      }
    }
  }

  /**
   * 记忆化函数
   */
  memoize<T extends any[], R>(
    fn: (...args: T) => R | Promise<R>,
    keyGenerator?: (...args: T) => string,
    ttl?: number
  ): (...args: T) => Promise<R> {
    return async (...args: T): Promise<R> => {
      const key = keyGenerator ? keyGenerator(...args) : JSON.stringify(args)
      
      // 尝试从缓存获取
      const cached = await this.get<R>(key)
      if (cached.success && cached.data !== null) {
        return cached.data
      }

      // 执行函数
      const result = await fn(...args)
      
      // 缓存结果
      await this.set(key, result, ttl)
      
      return result
    }
  }

  /**
   * 确保有足够空间
   */
  private async ensureSpace(requiredSize: number): Promise<void> {
    // 检查项数限制
    if (this.cache.size >= this.config.maxItems) {
      await this.evictItems(1)
    }

    // 检查大小限制
    const currentSize = this.getCurrentSize()
    if (currentSize + requiredSize > this.config.maxSize) {
      const sizeToFree = currentSize + requiredSize - this.config.maxSize
      await this.evictBySize(sizeToFree)
    }
  }

  /**
   * 驱逐缓存项
   */
  private async evictItems(count: number): Promise<void> {
    const items = Array.from(this.cache.entries())
    
    // 按最近最少使用排序
    items.sort((a, b) => a[1].lastAccessed - b[1].lastAccessed)
    
    for (let i = 0; i < Math.min(count, items.length); i++) {
      this.cache.delete(items[i][0])
    }
  }

  /**
   * 按大小驱逐缓存项
   */
  private async evictBySize(targetSize: number): Promise<void> {
    const items = Array.from(this.cache.entries())
    items.sort((a, b) => a[1].lastAccessed - b[1].lastAccessed)
    
    let freedSize = 0
    for (const [key, item] of items) {
      this.cache.delete(key)
      freedSize += item.size || 0
      
      if (freedSize >= targetSize) {
        break
      }
    }
  }

  /**
   * 计算值的大小
   */
  private calculateSize(value: any): number {
    try {
      return new Blob([JSON.stringify(value)]).size
    } catch {
      return 0
    }
  }

  /**
   * 获取当前缓存总大小
   */
  private getCurrentSize(): number {
    let totalSize = 0
    for (const item of this.cache.values()) {
      totalSize += item.size || 0
    }
    return totalSize
  }

  /**
   * 记录命中
   */
  private recordHit(): void {
    if (this.config.enableStats) {
      this.stats.hits++
    }
  }

  /**
   * 记录未命中
   */
  private recordMiss(): void {
    if (this.config.enableStats) {
      this.stats.misses++
    }
  }

  /**
   * 更新统计信息
   */
  private updateStats(): void {
    if (!this.config.enableStats) return

    this.stats.itemCount = this.cache.size
    this.stats.totalSize = this.getCurrentSize()
    
    const total = this.stats.hits + this.stats.misses
    this.stats.hitRate = total > 0 ? this.stats.hits / total : 0
  }

  /**
   * 清理过期项
   */
  private cleanup(): void {
    const now = Date.now()
    let expiredCount = 0

    for (const [key, item] of this.cache.entries()) {
      if (now > item.expiry) {
        this.cache.delete(key)
        expiredCount++
      }
    }

    this.stats.expiredItems += expiredCount
    this.updateStats()
  }

  /**
   * 启动清理定时器
   */
  private startCleanupTimer(): void {
    this.cleanupTimer = window.setInterval(() => {
      this.cleanup()
    }, this.config.cleanupInterval)
  }

  /**
   * 从存储加载缓存
   */
  private async loadFromStorage(): Promise<void> {
    try {
      const storage = this.getStorage()
      const data = storage.getItem('app_cache')
      
      if (data) {
        const parsed = JSON.parse(data)
        this.cache = new Map(parsed.cache)
        this.stats = parsed.stats || this.stats
      }
    } catch (error) {
      console.warn('Failed to load cache from storage:', error)
    }
  }

  /**
   * 保存缓存到存储
   */
  private async saveToStorage(): Promise<void> {
    try {
      const storage = this.getStorage()
      const data = {
        cache: Array.from(this.cache.entries()),
        stats: this.stats
      }
      
      storage.setItem('app_cache', JSON.stringify(data))
    } catch (error) {
      console.warn('Failed to save cache to storage:', error)
    }
  }

  /**
   * 获取存储对象
   */
  private getStorage(): Storage {
    switch (this.config.storage) {
      case 'localStorage':
        return localStorage
      case 'sessionStorage':
        return sessionStorage
      default:
        throw new Error('Invalid storage type for persistent cache')
    }
  }

  /**
   * 销毁服务
   */
  destroy(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer)
      this.cleanupTimer = undefined
    }
    
    this.cache.clear()
    this.resetStats()
  }
}

// 导出单例实例
export const cacheService = CacheService.getInstance()
