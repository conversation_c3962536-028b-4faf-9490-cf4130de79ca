<template>
  <div class="flex flex-col h-screen">
    <!-- 固定标题栏 -->
    <div class="fixed top-0 left-0 right-0 bg-white dark:bg-black border-b border-gray-200 dark:border-gray-800 px-4 py-4 safe-area-top z-30">
      <h1 class="text-lg font-semibold text-center">{{ $t('stats.title') }}</h1>
    </div>

    <!-- 可滚动内容区域 -->
    <div class="flex-1 overflow-y-auto pt-16 pb-20 safe-area-top">

      <!-- 月份选择 -->
      <div class="px-4 pt-4 mb-4">
        <BaseCard padding="sm">
          <div class="flex items-center justify-center">
            <button
              @click="previousMonth"
              class="flex items-center justify-center w-10 h-10 rounded-lg text-gray-500 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors duration-200"
            >
              <ChevronLeftIcon class="w-5 h-5" />
            </button>

            <div class="flex-1 text-center px-6">
              <span class="text-lg font-semibold">{{ currentMonthText }}</span>
            </div>

            <button
              @click="nextMonth"
              class="flex items-center justify-center w-10 h-10 rounded-lg text-gray-500 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
              :disabled="isCurrentMonth"
            >
              <ChevronRightIcon class="w-5 h-5" />
            </button>
          </div>
        </BaseCard>
      </div>

      <!-- 月度概览 -->
      <div class="px-4 mb-4">
        <div class="grid grid-cols-3 gap-3">
          <BaseCard padding="sm">
            <div class="text-center">
              <p class="text-sm text-gray-500 dark:text-gray-400">{{ $t('stats.income') }}</p>
              <p class="text-xl font-semibold text-green-500">
                {{ formatCurrency(monthlyIncome) }}
              </p>
            </div>
          </BaseCard>
          <BaseCard padding="sm">
            <div class="text-center">
              <p class="text-sm text-gray-500 dark:text-gray-400">{{ $t('stats.expense') }}</p>
              <p class="text-xl font-semibold text-red-500">
                {{ formatCurrency(monthlyExpense) }}
              </p>
            </div>
          </BaseCard>
          <BaseCard padding="sm">
            <div class="text-center">
              <p class="text-sm text-gray-500 dark:text-gray-400">{{ $t('stats.balance') }}</p>
              <p class="text-xl font-semibold" :class="[
                monthlyBalance >= 0 ? 'text-green-500' : 'text-red-500'
              ]">
                {{ formatCurrency(monthlyBalance) }}
              </p>
            </div>
          </BaseCard>
        </div>
      </div>



      <!-- 交易列表 -->
      <div class="px-4" v-if="monthlyTransactions.length > 0">
        <div class="mb-4">
          <h3 class="text-lg font-semibold">{{ $t('stats.transactions') }}</h3>
        </div>
        <div class="space-y-3">
          <div
            v-for="transaction in monthlyTransactions"
            :key="transaction.id"
            class="card p-4"
          >
            <div class="flex items-center">
              <div class="flex-shrink-0 mr-3">
                <div
                  class="w-10 h-10 rounded-full flex items-center justify-center text-lg"
                  :style="{ backgroundColor: getCategoryColor(transaction.categoryId) + '20' }"
                >
                  {{ getCategoryIcon(transaction.categoryId) }}
                </div>
              </div>

              <div class="flex-1 min-w-0">
                <div class="flex items-center justify-between">
                  <p class="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">
                    {{ getCategoryName(transaction.categoryId) }}
                  </p>
                  <p class="text-sm font-semibold" :class="[
                    transaction.type === 'income' ? 'text-green-500' : 'text-red-500'
                  ]">
                    {{ transaction.type === 'income' ? '+' : '-' }}{{ formatCurrency(transaction.amount) }}
                  </p>
                </div>
                <div class="mt-1">
                  <p class="text-xs text-gray-500 dark:text-gray-400 truncate">
                    {{ transaction.description || t('home.noNote') }}
                  </p>
                  <p class="text-xs text-gray-400 dark:text-gray-500">
                    {{ formatDate(transaction.date) }}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-else class="flex flex-col items-center justify-center min-h-[300px]">
        <p class="text-gray-400 dark:text-gray-500">{{ $t('stats.noTransactions') }}</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import {
  ChevronLeftIcon,
  ChevronRightIcon
} from '@heroicons/vue/24/outline'
import { BaseCard } from '@/components/ui'
import { useTransactionStore } from '@/stores/transactions'

const { t, locale } = useI18n()
const transactionStore = useTransactionStore()

// State
const selectedDate = ref(new Date())

// Computed properties
const currentMonthText = computed(() => {
  const localeMap = {
    'zh': 'zh-CN',
    'en': 'en-US'
  }
  const currentLocale = localeMap[locale.value as keyof typeof localeMap] || 'en-US'

  return selectedDate.value.toLocaleDateString(currentLocale, {
    year: 'numeric',
    month: 'long'
  })
})

const isCurrentMonth = computed(() => {
  const now = new Date()
  return selectedDate.value.getFullYear() === now.getFullYear() &&
         selectedDate.value.getMonth() === now.getMonth()
})

const monthlyTransactions = computed(() => {
  const year = selectedDate.value.getFullYear()
  const month = selectedDate.value.getMonth() + 1 // getMonth() returns 0-11, we need 1-12
  const monthString = `${year}-${month.toString().padStart(2, '0')}`
  return transactionStore.getTransactionsByMonth(monthString)
})

const monthlyIncome = computed(() => {
  return monthlyTransactions.value
    .filter(t => t.type === 'income')
    .reduce((sum, t) => sum + t.amount, 0)
})

const monthlyExpense = computed(() => {
  return monthlyTransactions.value
    .filter(t => t.type === 'expense')
    .reduce((sum, t) => sum + t.amount, 0)
})

const monthlyBalance = computed(() => {
  return monthlyIncome.value - monthlyExpense.value
})

// Methods
function previousMonth() {
  const newDate = new Date(selectedDate.value)
  newDate.setMonth(newDate.getMonth() - 1)
  selectedDate.value = newDate
}

function nextMonth() {
  if (!isCurrentMonth.value) {
    const newDate = new Date(selectedDate.value)
    newDate.setMonth(newDate.getMonth() + 1)
    selectedDate.value = newDate
  }
}

function formatCurrency(amount: number): string {
  return new Intl.NumberFormat('zh-CN', {
    style: 'currency',
    currency: 'CNY',
    minimumFractionDigits: 0,
    maximumFractionDigits: 2
  }).format(amount)
}

function formatDate(date: string): string {
  const d = new Date(date)
  const localeMap = {
    'zh': 'zh-CN',
    'en': 'en-US'
  }
  const currentLocale = localeMap[locale.value as keyof typeof localeMap] || 'en-US'

  return d.toLocaleDateString(currentLocale, {
    month: 'short',
    day: 'numeric'
  })
}

function getCategoryName(categoryId: string): string {
  const category = transactionStore.getCategoryById(categoryId)
  return category?.name || categoryId
}

function getCategoryIcon(categoryId: string): string {
  const category = transactionStore.getCategoryById(categoryId)
  return category?.icon || '💰'
}

function getCategoryColor(categoryId: string): string {
  const category = transactionStore.getCategoryById(categoryId)
  return category?.color || '#6b7280'
}

</script>
