/**
 * 错误处理服务
 * Error handling service
 */

import type { 
  ServiceError, 
  ErrorLevel, 
  ErrorContext, 
  ErrorHandler,
  ErrorReport 
} from '@/types'

/**
 * 错误处理服务类
 */
export class ErrorService {
  private static instance: ErrorService
  private handlers: Map<string, ErrorHandler[]> = new Map()
  private errorHistory: ErrorReport[] = []
  private maxHistorySize = 100

  private constructor() {
    this.setupGlobalErrorHandlers()
  }

  /**
   * 获取单例实例
   */
  static getInstance(): ErrorService {
    if (!ErrorService.instance) {
      ErrorService.instance = new ErrorService()
    }
    return ErrorService.instance
  }

  /**
   * 设置全局错误处理器
   */
  private setupGlobalErrorHandlers(): void {
    // 处理未捕获的JavaScript错误
    window.addEventListener('error', (event) => {
      this.handleError({
        code: 'UNCAUGHT_ERROR',
        message: event.message,
        level: 'error',
        context: {
          filename: event.filename,
          lineno: event.lineno,
          colno: event.colno,
          stack: event.error?.stack
        }
      })
    })

    // 处理未捕获的Promise拒绝
    window.addEventListener('unhandledrejection', (event) => {
      this.handleError({
        code: 'UNHANDLED_REJECTION',
        message: event.reason?.message || 'Unhandled promise rejection',
        level: 'error',
        context: {
          reason: event.reason,
          stack: event.reason?.stack
        }
      })
    })

    // 处理Vue错误（如果在Vue应用中）
    if (typeof window !== 'undefined' && (window as any).Vue) {
      (window as any).Vue.config.errorHandler = (err: Error, vm: any, info: string) => {
        this.handleError({
          code: 'VUE_ERROR',
          message: err.message,
          level: 'error',
          context: {
            componentInfo: info,
            stack: err.stack,
            vm: vm?.$options?.name || 'Unknown Component'
          }
        })
      }
    }
  }

  /**
   * 注册错误处理器
   */
  registerHandler(type: string, handler: ErrorHandler): void {
    if (!this.handlers.has(type)) {
      this.handlers.set(type, [])
    }
    this.handlers.get(type)!.push(handler)
  }

  /**
   * 移除错误处理器
   */
  removeHandler(type: string, handler: ErrorHandler): void {
    const handlers = this.handlers.get(type)
    if (handlers) {
      const index = handlers.indexOf(handler)
      if (index > -1) {
        handlers.splice(index, 1)
      }
    }
  }

  /**
   * 处理错误
   */
  handleError(error: ServiceError): void {
    const errorReport = this.createErrorReport(error)
    
    // 添加到错误历史
    this.addToHistory(errorReport)
    
    // 调用注册的处理器
    this.callHandlers(error)
    
    // 根据错误级别进行不同处理
    switch (error.level) {
      case 'critical':
        this.handleCriticalError(errorReport)
        break
      case 'error':
        this.handleErrorLevel(errorReport)
        break
      case 'warning':
        this.handleWarning(errorReport)
        break
      case 'info':
        this.handleInfo(errorReport)
        break
    }
  }

  /**
   * 创建错误报告
   */
  private createErrorReport(error: ServiceError): ErrorReport {
    return {
      id: this.generateErrorId(),
      timestamp: Date.now(),
      error,
      userAgent: navigator.userAgent,
      url: window.location.href,
      userId: this.getCurrentUserId(),
      sessionId: this.getSessionId()
    }
  }

  /**
   * 添加到错误历史
   */
  private addToHistory(errorReport: ErrorReport): void {
    this.errorHistory.unshift(errorReport)
    
    // 限制历史记录大小
    if (this.errorHistory.length > this.maxHistorySize) {
      this.errorHistory = this.errorHistory.slice(0, this.maxHistorySize)
    }
  }

  /**
   * 调用注册的处理器
   */
  private callHandlers(error: ServiceError): void {
    // 调用通用处理器
    const generalHandlers = this.handlers.get('*') || []
    generalHandlers.forEach(handler => {
      try {
        handler(error)
      } catch (handlerError) {
        console.error('Error in error handler:', handlerError)
      }
    })

    // 调用特定类型的处理器
    const specificHandlers = this.handlers.get(error.code) || []
    specificHandlers.forEach(handler => {
      try {
        handler(error)
      } catch (handlerError) {
        console.error('Error in specific error handler:', handlerError)
      }
    })
  }

  /**
   * 处理严重错误
   */
  private handleCriticalError(errorReport: ErrorReport): void {
    console.error('Critical Error:', errorReport)
    
    // 发送到错误监控服务
    this.sendToMonitoring(errorReport)
    
    // 显示用户友好的错误信息
    this.showUserError({
      title: '系统错误',
      message: '应用遇到了严重错误，请刷新页面重试。如果问题持续存在，请联系技术支持。',
      type: 'error',
      persistent: true
    })
  }

  /**
   * 处理一般错误
   */
  private handleErrorLevel(errorReport: ErrorReport): void {
    console.error('Error:', errorReport)
    
    // 发送到错误监控服务
    this.sendToMonitoring(errorReport)
    
    // 显示用户友好的错误信息
    this.showUserError({
      title: '操作失败',
      message: this.getUserFriendlyMessage(errorReport.error),
      type: 'error'
    })
  }

  /**
   * 处理警告
   */
  private handleWarning(errorReport: ErrorReport): void {
    console.warn('Warning:', errorReport)
    
    // 可选择性发送到监控服务
    if (this.shouldReportWarning(errorReport.error)) {
      this.sendToMonitoring(errorReport)
    }
  }

  /**
   * 处理信息
   */
  private handleInfo(errorReport: ErrorReport): void {
    console.info('Info:', errorReport)
  }

  /**
   * 发送到错误监控服务
   */
  private async sendToMonitoring(errorReport: ErrorReport): Promise<void> {
    try {
      // 这里可以集成第三方错误监控服务，如 Sentry、LogRocket 等
      // await fetch('/api/errors', {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify(errorReport)
      // })
      
      // 暂时存储到本地存储
      const existingReports = JSON.parse(localStorage.getItem('error_reports') || '[]')
      existingReports.unshift(errorReport)
      
      // 只保留最近的50个错误报告
      const reportsToStore = existingReports.slice(0, 50)
      localStorage.setItem('error_reports', JSON.stringify(reportsToStore))
    } catch (error) {
      console.error('Failed to send error report:', error)
    }
  }

  /**
   * 显示用户错误信息
   */
  private showUserError(options: {
    title: string
    message: string
    type: 'error' | 'warning' | 'info'
    persistent?: boolean
  }): void {
    // 这里可以集成通知系统
    // 暂时使用console输出
    console.log('User Error Notification:', options)
    
    // 可以触发全局事件，让UI组件监听并显示通知
    window.dispatchEvent(new CustomEvent('app:error', {
      detail: options
    }))
  }

  /**
   * 获取用户友好的错误信息
   */
  private getUserFriendlyMessage(error: ServiceError): string {
    const messageMap: Record<string, string> = {
      'NETWORK_ERROR': '网络连接失败，请检查网络设置后重试',
      'VALIDATION_ERROR': '输入的数据格式不正确，请检查后重试',
      'PERMISSION_DENIED': '您没有执行此操作的权限',
      'NOT_FOUND': '请求的资源不存在',
      'SERVER_ERROR': '服务器内部错误，请稍后重试',
      'TIMEOUT': '请求超时，请稍后重试',
      'STORAGE_FULL': '存储空间不足，请清理后重试'
    }

    return messageMap[error.code] || error.message || '发生了未知错误'
  }

  /**
   * 判断是否应该报告警告
   */
  private shouldReportWarning(error: ServiceError): boolean {
    // 可以根据错误类型、频率等条件决定是否报告
    return error.context?.reportable !== false
  }

  /**
   * 生成错误ID
   */
  private generateErrorId(): string {
    return `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * 获取当前用户ID
   */
  private getCurrentUserId(): string | null {
    // 这里可以从认证服务获取用户ID
    return localStorage.getItem('user_id') || null
  }

  /**
   * 获取会话ID
   */
  private getSessionId(): string {
    let sessionId = sessionStorage.getItem('session_id')
    if (!sessionId) {
      sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
      sessionStorage.setItem('session_id', sessionId)
    }
    return sessionId
  }

  /**
   * 获取错误历史
   */
  getErrorHistory(): ErrorReport[] {
    return [...this.errorHistory]
  }

  /**
   * 清除错误历史
   */
  clearErrorHistory(): void {
    this.errorHistory = []
  }

  /**
   * 创建错误
   */
  createError(
    code: string,
    message: string,
    level: ErrorLevel = 'error',
    context?: ErrorContext
  ): ServiceError {
    return {
      code,
      message,
      level,
      context,
      timestamp: Date.now()
    }
  }

  /**
   * 包装异步函数，自动处理错误
   */
  wrapAsync<T extends any[], R>(
    fn: (...args: T) => Promise<R>,
    errorCode?: string
  ): (...args: T) => Promise<R> {
    return async (...args: T): Promise<R> => {
      try {
        return await fn(...args)
      } catch (error) {
        const serviceError = this.createError(
          errorCode || 'ASYNC_ERROR',
          error instanceof Error ? error.message : 'Unknown async error',
          'error',
          { originalError: error }
        )
        this.handleError(serviceError)
        throw serviceError
      }
    }
  }
}

// 导出单例实例
export const errorService = ErrorService.getInstance()
