/**
 * 性能优化组合式函数
 * Performance optimization composable
 */

import { ref, computed, onMounted, onUnmounted, nextTick, defineAsyncComponent, h } from 'vue'
import { performanceService } from '@/services/PerformanceService'
import { cacheService } from '@/services/CacheService'
import type { PerformanceMetrics } from '@/services/PerformanceService'

/**
 * 性能优化组合式函数
 */
export function usePerformance() {
  // 响应式状态
  const metrics = ref<PerformanceMetrics[]>([])
  const isMonitoring = ref(false)
  const currentMetrics = ref<PerformanceMetrics | null>(null)

  // 计算属性
  const averageLoadTime = computed(() => {
    if (metrics.value.length === 0) return 0
    const total = metrics.value.reduce((sum, m) => sum + m.pageLoadTime, 0)
    return total / metrics.value.length
  })

  const averageFCP = computed(() => {
    if (metrics.value.length === 0) return 0
    const total = metrics.value.reduce((sum, m) => sum + m.firstContentfulPaint, 0)
    return total / metrics.value.length
  })

  const memoryUsage = computed(() => {
    return currentMetrics.value?.memoryUsage || null
  })

  const networkInfo = computed(() => {
    return currentMetrics.value?.networkInfo || null
  })

  /**
   * 初始化性能监控
   */
  const initializeMonitoring = async () => {
    try {
      await performanceService.initialize()
      await cacheService.initialize()
      isMonitoring.value = true
      
      // 获取初始指标
      await updateMetrics()
    } catch (error) {
      console.error('Failed to initialize performance monitoring:', error)
    }
  }

  /**
   * 更新性能指标
   */
  const updateMetrics = async () => {
    try {
      const result = await performanceService.getMetrics()
      if (result.success) {
        metrics.value = result.data
      }

      const latestResult = await performanceService.getLatestMetrics()
      if (latestResult.success) {
        currentMetrics.value = latestResult.data
      }
    } catch (error) {
      console.error('Failed to update metrics:', error)
    }
  }

  /**
   * 测量函数执行时间
   */
  const measureFunction = <T>(name: string, fn: () => T): T => {
    return performanceService.measureFunction(name, fn)
  }

  /**
   * 测量异步函数执行时间
   */
  const measureAsyncFunction = async <T>(
    name: string, 
    fn: () => Promise<T>
  ): Promise<T> => {
    return await performanceService.measureAsyncFunction(name, fn)
  }

  /**
   * 缓存函数结果
   */
  const memoize = <T extends any[], R>(
    fn: (...args: T) => R | Promise<R>,
    keyGenerator?: (...args: T) => string,
    ttl?: number
  ) => {
    return cacheService.memoize(fn, keyGenerator, ttl)
  }

  /**
   * 防抖函数
   */
  const debounce = <T extends any[]>(
    fn: (...args: T) => void,
    delay: number
  ) => {
    let timeoutId: number | undefined

    return (...args: T) => {
      clearTimeout(timeoutId)
      timeoutId = window.setTimeout(() => fn(...args), delay)
    }
  }

  /**
   * 节流函数
   */
  const throttle = <T extends any[]>(
    fn: (...args: T) => void,
    limit: number
  ) => {
    let inThrottle: boolean

    return (...args: T) => {
      if (!inThrottle) {
        fn(...args)
        inThrottle = true
        setTimeout(() => inThrottle = false, limit)
      }
    }
  }

  /**
   * 懒加载函数
   */
  const lazyLoad = <T>(loader: () => Promise<T>): (() => Promise<T>) => {
    let cached: T | null = null
    let loading = false
    let loadPromise: Promise<T> | null = null

    return async (): Promise<T> => {
      if (cached) {
        return cached
      }

      if (loading && loadPromise) {
        return loadPromise
      }

      loading = true
      loadPromise = loader().then(result => {
        cached = result
        loading = false
        return result
      }).catch(error => {
        loading = false
        loadPromise = null
        throw error
      })

      return loadPromise
    }
  }

  /**
   * 虚拟滚动辅助函数
   */
  const useVirtualScroll = <T>(
    items: T[],
    itemHeight: number,
    containerHeight: number,
    buffer: number = 5
  ) => {
    const scrollTop = ref(0)
    
    const visibleRange = computed(() => {
      const start = Math.floor(scrollTop.value / itemHeight)
      const end = Math.min(
        start + Math.ceil(containerHeight / itemHeight) + buffer,
        items.length
      )
      
      return {
        start: Math.max(0, start - buffer),
        end
      }
    })

    const visibleItems = computed(() => {
      const { start, end } = visibleRange.value
      return items.slice(start, end).map((item, index) => ({
        item,
        index: start + index
      }))
    })

    const totalHeight = computed(() => items.length * itemHeight)

    const offsetY = computed(() => visibleRange.value.start * itemHeight)

    const handleScroll = (event: Event) => {
      const target = event.target as HTMLElement
      scrollTop.value = target.scrollTop
    }

    return {
      visibleItems,
      totalHeight,
      offsetY,
      handleScroll
    }
  }

  /**
   * 图片懒加载
   */
  const useImageLazyLoad = () => {
    const imageObserver = ref<IntersectionObserver | null>(null)

    const observeImage = (img: HTMLImageElement, src: string) => {
      if (!imageObserver.value) {
        imageObserver.value = new IntersectionObserver((entries) => {
          entries.forEach(entry => {
            if (entry.isIntersecting) {
              const target = entry.target as HTMLImageElement
              const dataSrc = target.dataset.src
              if (dataSrc) {
                target.src = dataSrc
                target.removeAttribute('data-src')
                imageObserver.value?.unobserve(target)
              }
            }
          })
        })
      }

      img.dataset.src = src
      imageObserver.value.observe(img)
    }

    const cleanup = () => {
      if (imageObserver.value) {
        imageObserver.value.disconnect()
        imageObserver.value = null
      }
    }

    return {
      observeImage,
      cleanup
    }
  }

  /**
   * 组件懒加载
   */
  const lazyComponent = <T>(loader: () => Promise<T>) => {
    return defineAsyncComponent({
      loader,
      loadingComponent: {
        template: '<div class="loading">加载中...</div>'
      },
      errorComponent: {
        template: '<div class="error">加载失败</div>'
      },
      delay: 200,
      timeout: 3000
    })
  }

  /**
   * 批量更新DOM
   */
  const batchUpdate = async (updates: (() => void)[]): Promise<void> => {
    // 在下一个tick中批量执行更新
    await nextTick()
    
    updates.forEach(update => {
      try {
        update()
      } catch (error) {
        console.error('Batch update error:', error)
      }
    })
  }

  /**
   * 内存使用监控
   */
  const monitorMemory = () => {
    const checkMemory = () => {
      if ((performance as any).memory) {
        const memory = (performance as any).memory
        const usage = {
          used: memory.usedJSHeapSize,
          total: memory.totalJSHeapSize,
          limit: memory.jsHeapSizeLimit,
          percentage: (memory.usedJSHeapSize / memory.jsHeapSizeLimit) * 100
        }

        // 如果内存使用超过80%，发出警告
        if (usage.percentage > 80) {
          console.warn('High memory usage detected:', usage)
          
          // 触发垃圾回收（如果可用）
          if ((window as any).gc) {
            (window as any).gc()
          }
        }

        return usage
      }
      return null
    }

    return checkMemory
  }

  /**
   * 性能预算检查
   */
  const checkPerformanceBudget = (budgets: {
    loadTime?: number
    fcp?: number
    lcp?: number
    fid?: number
  }) => {
    const violations: string[] = []

    if (currentMetrics.value) {
      const { pageLoadTime, firstContentfulPaint, largestContentfulPaint, firstInputDelay } = currentMetrics.value

      if (budgets.loadTime && pageLoadTime > budgets.loadTime) {
        violations.push(`Page load time (${pageLoadTime}ms) exceeds budget (${budgets.loadTime}ms)`)
      }

      if (budgets.fcp && firstContentfulPaint > budgets.fcp) {
        violations.push(`FCP (${firstContentfulPaint}ms) exceeds budget (${budgets.fcp}ms)`)
      }

      if (budgets.lcp && largestContentfulPaint > budgets.lcp) {
        violations.push(`LCP (${largestContentfulPaint}ms) exceeds budget (${budgets.lcp}ms)`)
      }

      if (budgets.fid && firstInputDelay > budgets.fid) {
        violations.push(`FID (${firstInputDelay}ms) exceeds budget (${budgets.fid}ms)`)
      }
    }

    return violations
  }

  // 监听性能事件
  const handlePerformanceEvent = (event: CustomEvent) => {
    currentMetrics.value = event.detail.metrics
  }

  // 生命周期
  onMounted(() => {
    window.addEventListener('app:performance', handlePerformanceEvent as EventListener)
  })

  onUnmounted(() => {
    window.removeEventListener('app:performance', handlePerformanceEvent as EventListener)
  })

  return {
    // 状态
    metrics: readonly(metrics),
    isMonitoring: readonly(isMonitoring),
    currentMetrics: readonly(currentMetrics),
    
    // 计算属性
    averageLoadTime,
    averageFCP,
    memoryUsage,
    networkInfo,
    
    // 方法
    initializeMonitoring,
    updateMetrics,
    measureFunction,
    measureAsyncFunction,
    memoize,
    debounce,
    throttle,
    lazyLoad,
    useVirtualScroll,
    useImageLazyLoad,
    lazyComponent,
    batchUpdate,
    monitorMemory,
    checkPerformanceBudget
  }
}

/**
 * 性能装饰器
 */
export function withPerformanceMonitoring(name: string) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value

    descriptor.value = function (...args: any[]) {
      return performanceService.measureFunction(name, () => method.apply(this, args))
    }
  }
}

/**
 * 异步性能装饰器
 */
export function withAsyncPerformanceMonitoring(name: string) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value

    descriptor.value = async function (...args: any[]) {
      return await performanceService.measureAsyncFunction(name, () => method.apply(this, args))
    }
  }
}
