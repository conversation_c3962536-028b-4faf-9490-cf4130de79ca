# Money Tracker PWA

一个专为手机端设计的PWA记账应用，使用现代Web技术构建。

## 技术栈

- **前端框架**: Vue 3 + TypeScript
- **构建工具**: Vite
- **样式框架**: Tailwind CSS
- **状态管理**: Pinia
- **路由**: Vue Router
- **PWA**: Vite PWA Plugin + Workbox
- **图标**: Heroicons
- **部署**: Vercel

## 功能特性

- ✅ 移动端优先的响应式设计
- ✅ 原生APP般的用户体验
- ✅ 支持浅色/深色主题切换
- ✅ PWA支持，可安装到桌面
- ✅ 离线缓存功能
- ✅ 收入/支出记录管理
- ✅ 分类统计分析
- ✅ 数据本地存储
- ✅ 数据导出功能

## 开发环境设置

### 前置要求

- Node.js 20.19.0+ 或 22.12.0+
- npm 或 yarn

### 安装依赖

```bash
npm install
```

### 开发服务器

```bash
npm run dev
```

应用将在 http://localhost:3000 启动

### 构建生产版本

```bash
npm run build
```

### 预览生产版本

```bash
npm run preview
```

## PWA图标生成

项目包含了一个SVG图标文件 `public/icon.svg`。要生成不同尺寸的PNG图标，可以使用以下方法：

### 使用ImageMagick (推荐)

```bash
# 安装ImageMagick后执行
convert public/icon.svg -resize 64x64 public/pwa-64x64.png
convert public/icon.svg -resize 192x192 public/pwa-192x192.png
convert public/icon.svg -resize 512x512 public/pwa-512x512.png
convert public/icon.svg -resize 512x512 public/maskable-icon-512x512.png
convert public/icon.svg -resize 180x180 public/apple-touch-icon.png
```

### 在线工具

- [PWA Icon Generator](https://www.pwabuilder.com/imageGenerator)
- [Favicon Generator](https://favicon.io/)
- [Real Favicon Generator](https://realfavicongenerator.net/)

## 项目结构

```
src/
├── assets/          # 静态资源
├── components/      # Vue组件
├── composables/     # 组合式函数
├── stores/          # Pinia状态管理
├── types/           # TypeScript类型定义
├── views/           # 页面组件
├── App.vue          # 根组件
└── main.ts          # 应用入口

public/
├── manifest.json    # PWA清单文件
├── icon.svg         # 应用图标源文件
└── *.png           # 各尺寸图标文件
```

## 设计规范

### 颜色系统

- **主色**: `#0ea5e9` (天蓝色)
- **成功色**: `#22c55e` (绿色)
- **危险色**: `#ef4444` (红色)
- **灰色系**: Tailwind CSS 默认灰色调色板

### 字体

- 系统字体栈: `-apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Helvetica Neue, Arial, sans-serif`

### 间距

- 基础间距单位: `4px` (Tailwind CSS 默认)
- 安全区域适配: 使用 `env(safe-area-inset-*)`

### 组件规范

- 按钮: 圆角 `8px`，最小触摸区域 `44px`
- 卡片: 圆角 `12px`，阴影 `shadow-sm`
- 输入框: 圆角 `8px`，边框 `1px`

## 浏览器支持

- iOS Safari 14+
- Chrome 88+
- Firefox 78+
- Edge 88+

## 部署

### Vercel部署

1. 将代码推送到GitHub
2. 在Vercel中导入项目
3. 构建命令: `npm run build`
4. 输出目录: `dist`

### 其他平台

项目构建后的 `dist` 目录可以部署到任何静态文件托管服务：

- Netlify
- GitHub Pages
- Firebase Hosting
- Cloudflare Pages

## 开发指南

### 添加新页面

1. 在 `src/views/` 创建Vue组件
2. 在 `src/main.ts` 中添加路由配置
3. 如需要，在底部导航中添加入口

### 添加新的数据类型

1. 在 `src/types/index.ts` 中定义TypeScript接口
2. 在相应的Pinia store中添加状态和操作方法
3. 更新本地存储逻辑

### 自定义主题

1. 修改 `tailwind.config.js` 中的颜色配置
2. 更新 `src/assets/main.css` 中的CSS变量
3. 调整PWA manifest中的主题色

## 许可证

MIT License
