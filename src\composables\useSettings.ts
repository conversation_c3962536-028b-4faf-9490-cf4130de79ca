import { computed, ref } from 'vue'
import { useSettingsStore } from '@/stores/settings'
import type { AppSettings, ThemeMode, LanguageCode } from '@/types'

/**
 * 应用设置管理组合式函数
 * 提供设置相关的响应式数据和操作方法
 */
export function useSettings() {
  const store = useSettingsStore()

  // ==========================================================================
  // 响应式状态 - Reactive State
  // ==========================================================================

  /** 是否显示加载状态 */
  const showLoading = ref(false)

  // ==========================================================================
  // 计算属性 - Computed Properties
  // ==========================================================================

  /** 当前设置 */
  const settings = computed(() => store.settings)

  /** 当前主题 */
  const currentTheme = computed(() => store.currentTheme)

  /** 是否为深色模式 */
  const isDarkMode = computed(() => store.isDarkMode)

  /** 货币格式化器 */
  const currencyFormatter = computed(() => store.currencyFormatter)

  /** 是否正在加载 */
  const isLoading = computed(() => store.isLoading || showLoading.value)

  /** 是否有错误 */
  const hasError = computed(() => store.hasError)

  /** 错误信息 */
  const errorMessage = computed(() => store.errorMessage)

  // ==========================================================================
  // 设置操作方法 - Settings Actions
  // ==========================================================================

  /** 初始化设置 */
  async function initialize() {
    showLoading.value = true
    try {
      await store.initialize()
    } finally {
      showLoading.value = false
    }
  }

  /** 刷新设置 */
  async function refresh() {
    showLoading.value = true
    try {
      await store.loadSettings()
    } finally {
      showLoading.value = false
    }
  }

  /** 更新设置 */
  async function updateSettings(updates: Partial<AppSettings>) {
    showLoading.value = true
    try {
      await store.updateSettings(updates)
      return { success: true }
    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      }
    } finally {
      showLoading.value = false
    }
  }

  // ==========================================================================
  // 主题管理方法 - Theme Management Methods
  // ==========================================================================

  /** 更新主题 */
  async function updateTheme(theme: ThemeMode) {
    showLoading.value = true
    try {
      await store.updateTheme(theme)
      return { success: true }
    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Failed to update theme' 
      }
    } finally {
      showLoading.value = false
    }
  }

  /** 切换主题 */
  async function toggleTheme() {
    const themes: ThemeMode[] = ['light', 'dark', 'system']
    const currentIndex = themes.indexOf(settings.value.theme)
    const nextIndex = (currentIndex + 1) % themes.length
    return await updateTheme(themes[nextIndex])
  }

  /** 获取主题选项 */
  function getThemeOptions(): Array<{ value: ThemeMode; label: string; icon: string }> {
    return [
      { value: 'light', label: '浅色模式', icon: 'sun' },
      { value: 'dark', label: '深色模式', icon: 'moon' },
      { value: 'system', label: '跟随系统', icon: 'computer-desktop' }
    ]
  }

  // ==========================================================================
  // 货币管理方法 - Currency Management Methods (固定使用人民币)
  // ==========================================================================

  /** 格式化货币 - 固定使用人民币 */
  function formatCurrency(amount: number): string {
    return store.formatCurrency(amount)
  }

  // ==========================================================================
  // 语言管理方法 - Language Management Methods
  // ==========================================================================

  /** 更新语言 */
  async function updateLanguage(language: LanguageCode) {
    showLoading.value = true
    try {
      await store.updateLanguage(language)
      return { success: true }
    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Failed to update language' 
      }
    } finally {
      showLoading.value = false
    }
  }

  /** 获取语言选项 */
  function getLanguageOptions(): Array<{ value: LanguageCode; label: string }> {
    return [
      { value: 'zh-CN', label: '简体中文' },
      { value: 'zh-TW', label: '繁體中文' },
      { value: 'en-US', label: 'English' },
      { value: 'ja-JP', label: '日本語' },
      { value: 'ko-KR', label: '한국어' }
    ]
  }

  // ==========================================================================
  // 通知管理方法 - Notification Management Methods
  // ==========================================================================

  /** 切换通知设置 */
  async function toggleNotifications() {
    showLoading.value = true
    try {
      await store.toggleNotifications()
      return { success: true }
    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Failed to toggle notifications' 
      }
    } finally {
      showLoading.value = false
    }
  }

  // ==========================================================================
  // 生物识别管理方法 - Biometric Management Methods
  // ==========================================================================

  /** 切换生物识别设置 */
  async function toggleBiometric() {
    showLoading.value = true
    try {
      await store.toggleBiometric()
      return { success: true }
    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Failed to toggle biometric' 
      }
    } finally {
      showLoading.value = false
    }
  }

  // ==========================================================================
  // 备份管理方法 - Backup Management Methods
  // ==========================================================================

  /** 更新备份设置 */
  async function updateBackupSettings(backupSettings: Partial<AppSettings['backup']>) {
    showLoading.value = true
    try {
      await store.updateBackupSettings(backupSettings)
      return { success: true }
    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Failed to update backup settings' 
      }
    } finally {
      showLoading.value = false
    }
  }

  /** 切换备份功能 */
  async function toggleBackup() {
    const newEnabled = !settings.value.backup.enabled
    return await updateBackupSettings({ enabled: newEnabled })
  }

  /** 更新备份频率 */
  async function updateBackupFrequency(frequency: number) {
    return await updateBackupSettings({ frequency })
  }

  /** 获取备份频率选项 */
  function getBackupFrequencyOptions(): Array<{ value: number; label: string }> {
    return [
      { value: 1, label: '每天' },
      { value: 3, label: '每3天' },
      { value: 7, label: '每周' },
      { value: 14, label: '每两周' },
      { value: 30, label: '每月' }
    ]
  }

  // ==========================================================================
  // 数据管理方法 - Data Management Methods
  // ==========================================================================

  /** 重置设置 */
  async function resetSettings() {
    showLoading.value = true
    try {
      await store.resetSettings()
      return { success: true }
    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Failed to reset settings' 
      }
    } finally {
      showLoading.value = false
    }
  }

  /** 导出设置 */
  function exportSettings(): string {
    return store.exportSettings()
  }

  /** 导入设置 */
  async function importSettings(settingsJson: string) {
    showLoading.value = true
    try {
      const success = await store.importSettings(settingsJson)
      if (success) {
        return { success: true }
      } else {
        return { success: false, error: '导入的设置格式不正确' }
      }
    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Failed to import settings' 
      }
    } finally {
      showLoading.value = false
    }
  }

  // ==========================================================================
  // 工具方法 - Utility Methods
  // ==========================================================================

  /** 清除错误状态 */
  function clearError() {
    store.clearError()
  }

  /** 验证设置数据 */
  function validateSettings(settingsData: Partial<AppSettings>): {
    valid: boolean
    errors: string[]
  } {
    const errors: string[] = []

    // 验证主题
    if (settingsData.theme && !['light', 'dark', 'system'].includes(settingsData.theme)) {
      errors.push('主题设置无效')
    }

    // 验证货币 - 固定使用人民币，不需要验证
    // 货币已固定为CNY，无需验证

    // 验证语言
    const validLanguages = ['zh-CN', 'zh-TW', 'en-US', 'ja-JP', 'ko-KR']
    if (settingsData.language && !validLanguages.includes(settingsData.language)) {
      errors.push('语言设置无效')
    }

    // 验证备份频率
    if (settingsData.backup?.frequency !== undefined) {
      const frequency = settingsData.backup.frequency
      if (!Number.isInteger(frequency) || frequency < 1 || frequency > 365) {
        errors.push('备份频率必须是1-365之间的整数')
      }
    }

    return {
      valid: errors.length === 0,
      errors
    }
  }

  // ==========================================================================
  // 返回接口 - Return Interface
  // ==========================================================================

  return {
    // 响应式状态
    isLoading,
    hasError,
    errorMessage,

    // 计算属性
    settings,
    currentTheme,
    isDarkMode,
    currencyFormatter,

    // 数据操作方法
    initialize,
    refresh,
    updateSettings,

    // 主题管理
    updateTheme,
    toggleTheme,
    getThemeOptions,

    // 货币管理
    formatCurrency,

    // 语言管理
    updateLanguage,
    getLanguageOptions,

    // 通知管理
    toggleNotifications,

    // 生物识别管理
    toggleBiometric,

    // 备份管理
    updateBackupSettings,
    toggleBackup,
    updateBackupFrequency,
    getBackupFrequencyOptions,

    // 数据管理
    resetSettings,
    exportSettings,
    importSettings,

    // 工具方法
    clearError,
    validateSettings
  }
}
