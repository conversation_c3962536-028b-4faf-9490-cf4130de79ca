import { BaseService } from '../base/BaseService'
import { storageService } from '../storage/StorageService'
import { validationService } from '../validation/ValidationService'
import type { 
  Transaction, 
  TransactionType, 
  TransactionFilter,
  ServiceResponse,
  PaginatedResult,
  PaginationParams
} from '@/types'

/**
 * 交易数据服务
 * 负责交易数据的CRUD操作和业务逻辑
 */
export class TransactionService extends BaseService {
  private static instance: TransactionService
  private readonly STORAGE_KEY = 'transactions'
  private transactionsCache: Transaction[] | null = null
  private lastCacheUpdate: number = 0
  private readonly CACHE_TTL = 5 * 60 * 1000 // 5分钟缓存

  private constructor() {
    super()
  }

  /**
   * 获取单例实例
   */
  public static getInstance(): TransactionService {
    if (!TransactionService.instance) {
      TransactionService.instance = new TransactionService()
    }
    return TransactionService.instance
  }

  /**
   * 获取所有交易记录
   */
  public async getAllTransactions(forceRefresh: boolean = false): Promise<ServiceResponse<Transaction[]>> {
    return this.safeExecute(async () => {
      // 检查缓存
      if (!forceRefresh && this.isCacheValid()) {
        return this.transactionsCache!
      }

      const result = await storageService.getItem<Transaction[]>(this.STORAGE_KEY, [])
      if (!result.success) {
        throw new Error(result.error?.message || 'Failed to load transactions')
      }

      // 更新缓存
      this.transactionsCache = result.data || []
      this.lastCacheUpdate = Date.now()

      return this.transactionsCache
    }, 'GET_TRANSACTIONS_ERROR')
  }

  /**
   * 根据ID获取交易记录
   */
  public async getTransactionById(id: string): Promise<ServiceResponse<Transaction | null>> {
    return this.safeExecute(async () => {
      const transactionsResult = await this.getAllTransactions()
      if (!transactionsResult.success) {
        throw new Error('Failed to load transactions')
      }

      const transaction = transactionsResult.data!.find(t => t.id === id)
      return transaction || null
    }, 'GET_TRANSACTION_BY_ID_ERROR')
  }

  /**
   * 创建交易记录
   */
  public async createTransaction(
    transactionData: Omit<Transaction, 'id' | 'createdAt' | 'updatedAt'>
  ): Promise<ServiceResponse<Transaction>> {
    return this.safeExecute(async () => {
      // 验证数据
      const validationResult = validationService.validateTransaction(transactionData)
      if (!validationResult.success || !validationResult.data!.valid) {
        const errors = validationResult.data?.errors || []
        throw new Error(`Validation failed: ${errors.map(e => e.message).join(', ')}`)
      }

      // 创建新交易记录
      const now = new Date().toISOString()
      const transaction: Transaction = {
        ...transactionData,
        id: this.generateId(),
        createdAt: now,
        updatedAt: now
      }

      // 获取现有交易记录
      const transactionsResult = await this.getAllTransactions()
      if (!transactionsResult.success) {
        throw new Error('Failed to load existing transactions')
      }

      // 添加新记录并保存
      const transactions = [...transactionsResult.data!, transaction]
      const saveResult = await storageService.setItem(this.STORAGE_KEY, transactions)
      
      if (!saveResult.success) {
        throw new Error(saveResult.error?.message || 'Failed to save transaction')
      }

      // 更新缓存
      this.transactionsCache = transactions
      this.lastCacheUpdate = Date.now()

      this.log('info', 'Transaction created successfully', { id: transaction.id })
      return transaction
    }, 'CREATE_TRANSACTION_ERROR')
  }

  /**
   * 更新交易记录
   */
  public async updateTransaction(
    id: string,
    updates: Partial<Omit<Transaction, 'id' | 'createdAt'>>
  ): Promise<ServiceResponse<Transaction>> {
    return this.safeExecute(async () => {
      // 验证更新数据
      if (Object.keys(updates).length > 0) {
        const validationResult = validationService.validateTransaction(updates)
        if (!validationResult.success || !validationResult.data!.valid) {
          const errors = validationResult.data?.errors || []
          throw new Error(`Validation failed: ${errors.map(e => e.message).join(', ')}`)
        }
      }

      // 获取现有交易记录
      const transactionsResult = await this.getAllTransactions()
      if (!transactionsResult.success) {
        throw new Error('Failed to load transactions')
      }

      const transactions = transactionsResult.data!
      const index = transactions.findIndex(t => t.id === id)
      
      if (index === -1) {
        throw new Error(`Transaction with id ${id} not found`)
      }

      // 更新记录
      const updatedTransaction: Transaction = {
        ...transactions[index],
        ...updates,
        updatedAt: new Date().toISOString()
      }

      transactions[index] = updatedTransaction

      // 保存更新
      const saveResult = await storageService.setItem(this.STORAGE_KEY, transactions)
      if (!saveResult.success) {
        throw new Error(saveResult.error?.message || 'Failed to save updated transaction')
      }

      // 更新缓存
      this.transactionsCache = transactions
      this.lastCacheUpdate = Date.now()

      this.log('info', 'Transaction updated successfully', { id })
      return updatedTransaction
    }, 'UPDATE_TRANSACTION_ERROR')
  }

  /**
   * 删除交易记录
   */
  public async deleteTransaction(id: string): Promise<ServiceResponse<boolean>> {
    return this.safeExecute(async () => {
      // 获取现有交易记录
      const transactionsResult = await this.getAllTransactions()
      if (!transactionsResult.success) {
        throw new Error('Failed to load transactions')
      }

      const transactions = transactionsResult.data!
      const index = transactions.findIndex(t => t.id === id)
      
      if (index === -1) {
        throw new Error(`Transaction with id ${id} not found`)
      }

      // 删除记录
      transactions.splice(index, 1)

      // 保存更新
      const saveResult = await storageService.setItem(this.STORAGE_KEY, transactions)
      if (!saveResult.success) {
        throw new Error(saveResult.error?.message || 'Failed to save after deletion')
      }

      // 更新缓存
      this.transactionsCache = transactions
      this.lastCacheUpdate = Date.now()

      this.log('info', 'Transaction deleted successfully', { id })
      return true
    }, 'DELETE_TRANSACTION_ERROR')
  }

  /**
   * 批量删除交易记录
   */
  public async deleteTransactions(ids: string[]): Promise<ServiceResponse<number>> {
    return this.safeExecute(async () => {
      if (ids.length === 0) {
        return 0
      }

      // 获取现有交易记录
      const transactionsResult = await this.getAllTransactions()
      if (!transactionsResult.success) {
        throw new Error('Failed to load transactions')
      }

      const transactions = transactionsResult.data!
      const initialCount = transactions.length

      // 过滤掉要删除的记录
      const filteredTransactions = transactions.filter(t => !ids.includes(t.id))
      const deletedCount = initialCount - filteredTransactions.length

      // 保存更新
      const saveResult = await storageService.setItem(this.STORAGE_KEY, filteredTransactions)
      if (!saveResult.success) {
        throw new Error(saveResult.error?.message || 'Failed to save after batch deletion')
      }

      // 更新缓存
      this.transactionsCache = filteredTransactions
      this.lastCacheUpdate = Date.now()

      this.log('info', 'Batch deletion completed', { deletedCount, requestedIds: ids.length })
      return deletedCount
    }, 'BATCH_DELETE_TRANSACTIONS_ERROR')
  }

  /**
   * 生成唯一ID
   */
  private generateId(): string {
    return `txn_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * 检查缓存是否有效
   */
  private isCacheValid(): boolean {
    return this.transactionsCache !== null && 
           (Date.now() - this.lastCacheUpdate) < this.CACHE_TTL
  }

  /**
   * 清除缓存
   */
  public clearCache(): void {
    this.transactionsCache = null
    this.lastCacheUpdate = 0
  }
}

// 导出单例实例
export const transactionService = TransactionService.getInstance()
