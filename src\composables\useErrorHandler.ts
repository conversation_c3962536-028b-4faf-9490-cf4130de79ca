/**
 * 错误处理组合式函数
 * Error handling composable
 */

import { ref, computed, onMounted, onUnmounted, readonly } from 'vue'
import { errorService } from '@/services/ErrorService'
import type { ServiceError, ErrorLevel, ErrorContext, ErrorReport } from '@/types'

/**
 * 错误处理组合式函数
 */
export function useErrorHandler() {
  // 响应式状态
  const errors = ref<ServiceError[]>([])
  const isHandlingError = ref(false)
  const lastError = ref<ServiceError | null>(null)

  // 计算属性
  const hasErrors = computed(() => errors.value.length > 0)
  const criticalErrors = computed(() => 
    errors.value.filter(error => error.level === 'critical')
  )
  const errorCount = computed(() => errors.value.length)

  /**
   * 处理错误
   */
  const handleError = (
    error: Error | ServiceError | string,
    code?: string,
    level: ErrorLevel = 'error',
    context?: ErrorContext
  ): void => {
    let serviceError: ServiceError

    if (typeof error === 'string') {
      serviceError = errorService.createError(
        code || 'GENERIC_ERROR',
        error,
        level,
        context
      )
    } else if (error instanceof Error) {
      serviceError = errorService.createError(
        code || 'JS_ERROR',
        error.message,
        level,
        {
          ...context,
          stack: error.stack,
          name: error.name
        }
      )
    } else {
      serviceError = error
    }

    // 添加到本地错误列表
    errors.value.unshift(serviceError)
    lastError.value = serviceError

    // 限制错误列表大小
    if (errors.value.length > 50) {
      errors.value = errors.value.slice(0, 50)
    }

    // 使用错误服务处理
    errorService.handleError(serviceError)
  }

  /**
   * 清除错误
   */
  const clearError = (index?: number): void => {
    if (typeof index === 'number') {
      errors.value.splice(index, 1)
    } else {
      errors.value = []
      lastError.value = null
    }
  }

  /**
   * 清除特定类型的错误
   */
  const clearErrorsByCode = (code: string): void => {
    errors.value = errors.value.filter(error => error.code !== code)
    if (lastError.value?.code === code) {
      lastError.value = errors.value[0] || null
    }
  }

  /**
   * 包装异步函数
   */
  const wrapAsync = <T extends any[], R>(
    fn: (...args: T) => Promise<R>,
    errorCode?: string,
    errorLevel: ErrorLevel = 'error'
  ) => {
    return async (...args: T): Promise<R | null> => {
      isHandlingError.value = true
      try {
        const result = await fn(...args)
        return result
      } catch (error) {
        handleError(
          error as Error,
          errorCode,
          errorLevel,
          { functionName: fn.name }
        )
        return null
      } finally {
        isHandlingError.value = false
      }
    }
  }

  /**
   * 包装同步函数
   */
  const wrapSync = <T extends any[], R>(
    fn: (...args: T) => R,
    errorCode?: string,
    errorLevel: ErrorLevel = 'error'
  ) => {
    return (...args: T): R | null => {
      try {
        return fn(...args)
      } catch (error) {
        handleError(
          error as Error,
          errorCode,
          errorLevel,
          { functionName: fn.name }
        )
        return null
      }
    }
  }

  /**
   * 重试函数
   */
  const retry = async <T>(
    fn: () => Promise<T>,
    maxAttempts: number = 3,
    delay: number = 1000,
    backoff: boolean = true
  ): Promise<T | null> => {
    let lastError: Error | null = null
    
    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
      try {
        return await fn()
      } catch (error) {
        lastError = error as Error
        
        if (attempt === maxAttempts) {
          handleError(
            lastError,
            'RETRY_FAILED',
            'error',
            { attempts: maxAttempts, finalAttempt: true }
          )
          break
        }

        // 计算延迟时间
        const currentDelay = backoff ? delay * Math.pow(2, attempt - 1) : delay
        await new Promise(resolve => setTimeout(resolve, currentDelay))
      }
    }
    
    return null
  }

  /**
   * 创建错误边界
   */
  const createErrorBoundary = (
    fallback?: () => void,
    onError?: (error: ServiceError) => void
  ) => {
    return {
      onError: (error: Error, info: any) => {
        const serviceError = errorService.createError(
          'COMPONENT_ERROR',
          error.message,
          'error',
          {
            componentInfo: info,
            stack: error.stack
          }
        )
        
        handleError(serviceError)
        
        if (onError) {
          onError(serviceError)
        }
        
        if (fallback) {
          fallback()
        }
      }
    }
  }

  /**
   * 验证并处理表单错误
   */
  const handleValidationError = (
    validationResult: any,
    fieldName?: string
  ): boolean => {
    if (validationResult && validationResult.error) {
      handleError(
        validationResult.error,
        'VALIDATION_ERROR',
        'warning',
        { field: fieldName }
      )
      return false
    }
    return true
  }

  /**
   * 处理网络错误
   */
  const handleNetworkError = (
    error: Error,
    url?: string,
    method?: string
  ): void => {
    handleError(
      error,
      'NETWORK_ERROR',
      'error',
      {
        url,
        method,
        isOnline: navigator.onLine
      }
    )
  }

  /**
   * 处理存储错误
   */
  const handleStorageError = (
    error: Error,
    operation: 'read' | 'write' | 'delete',
    key?: string
  ): void => {
    handleError(
      error,
      'STORAGE_ERROR',
      'warning',
      {
        operation,
        key,
        storageAvailable: typeof Storage !== 'undefined'
      }
    )
  }

  /**
   * 获取错误统计
   */
  const getErrorStats = () => {
    const stats = {
      total: errors.value.length,
      critical: 0,
      error: 0,
      warning: 0,
      info: 0
    }

    errors.value.forEach(error => {
      stats[error.level]++
    })

    return stats
  }

  /**
   * 导出错误报告
   */
  const exportErrorReport = (): string => {
    const report = {
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href,
      errors: errors.value,
      stats: getErrorStats()
    }

    return JSON.stringify(report, null, 2)
  }

  // 监听全局错误事件
  const handleGlobalError = (event: CustomEvent) => {
    const { title, message, type } = event.detail
    handleError(
      message,
      'GLOBAL_ERROR',
      type === 'error' ? 'error' : 'warning',
      { title, source: 'global' }
    )
  }

  // 生命周期
  onMounted(() => {
    window.addEventListener('app:error', handleGlobalError as EventListener)
  })

  onUnmounted(() => {
    window.removeEventListener('app:error', handleGlobalError as EventListener)
  })

  return {
    // 状态
    errors: readonly(errors),
    isHandlingError: readonly(isHandlingError),
    lastError: readonly(lastError),
    
    // 计算属性
    hasErrors,
    criticalErrors,
    errorCount,
    
    // 方法
    handleError,
    clearError,
    clearErrorsByCode,
    wrapAsync,
    wrapSync,
    retry,
    createErrorBoundary,
    handleValidationError,
    handleNetworkError,
    handleStorageError,
    getErrorStats,
    exportErrorReport
  }
}

/**
 * 全局错误处理器实例
 */
export const globalErrorHandler = useErrorHandler()

/**
 * 错误处理装饰器
 */
export function withErrorHandling<T extends any[], R>(
  target: (...args: T) => Promise<R>,
  errorCode?: string
): (...args: T) => Promise<R | null> {
  return globalErrorHandler.wrapAsync(target, errorCode)
}

/**
 * 同步错误处理装饰器
 */
export function withSyncErrorHandling<T extends any[], R>(
  target: (...args: T) => R,
  errorCode?: string
): (...args: T) => R | null {
  return globalErrorHandler.wrapSync(target, errorCode)
}
