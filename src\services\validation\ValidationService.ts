import { BaseService } from '../base/BaseService'
import type { ValidationResult, ValidationError, Transaction, Category, ServiceResponse } from '@/types'

/**
 * 验证服务
 * 提供统一的数据验证功能
 */
export class ValidationService extends BaseService {
  private static instance: ValidationService

  private constructor() {
    super()
  }

  /**
   * 获取单例实例
   */
  public static getInstance(): ValidationService {
    if (!ValidationService.instance) {
      ValidationService.instance = new ValidationService()
    }
    return ValidationService.instance
  }

  /**
   * 验证交易数据
   */
  public validateTransaction(data: Partial<Transaction>): ServiceResponse<ValidationResult> {
    return this.safeExecuteSync(() => {
      const errors: ValidationError[] = []

      // 验证金额
      if (!data.amount || data.amount <= 0) {
        errors.push({
          field: 'amount',
          message: '金额必须大于0',
          code: 'INVALID_AMOUNT'
        })
      }

      // 验证分类ID
      if (!data.categoryId || data.categoryId.trim() === '') {
        errors.push({
          field: 'categoryId',
          message: '请选择分类',
          code: 'MISSING_CATEGORY'
        })
      }

      // 验证交易类型
      if (!data.type || !['income', 'expense'].includes(data.type)) {
        errors.push({
          field: 'type',
          message: '交易类型无效',
          code: 'INVALID_TYPE'
        })
      }

      // 验证日期
      if (!data.date || !this.isValidDate(data.date)) {
        errors.push({
          field: 'date',
          message: '日期格式无效',
          code: 'INVALID_DATE'
        })
      }

      // 验证描述长度
      if (data.description && data.description.length > 200) {
        errors.push({
          field: 'description',
          message: '描述不能超过200个字符',
          code: 'DESCRIPTION_TOO_LONG'
        })
      }

      return {
        valid: errors.length === 0,
        errors
      }
    }, 'TRANSACTION_VALIDATION_ERROR')
  }

  /**
   * 验证分类数据
   */
  public validateCategory(data: Partial<Category>): ServiceResponse<ValidationResult> {
    return this.safeExecuteSync(() => {
      const errors: ValidationError[] = []

      // 验证名称
      if (!data.name || data.name.trim() === '') {
        errors.push({
          field: 'name',
          message: '分类名称不能为空',
          code: 'MISSING_NAME'
        })
      } else if (data.name.length > 20) {
        errors.push({
          field: 'name',
          message: '分类名称不能超过20个字符',
          code: 'NAME_TOO_LONG'
        })
      }

      // 验证图标
      if (!data.icon || data.icon.trim() === '') {
        errors.push({
          field: 'icon',
          message: '请选择图标',
          code: 'MISSING_ICON'
        })
      }

      // 验证颜色
      if (!data.color || !this.isValidHexColor(data.color)) {
        errors.push({
          field: 'color',
          message: '颜色格式无效',
          code: 'INVALID_COLOR'
        })
      }

      // 验证类型
      if (!data.type || !['income', 'expense'].includes(data.type)) {
        errors.push({
          field: 'type',
          message: '分类类型无效',
          code: 'INVALID_TYPE'
        })
      }

      // 验证排序权重
      if (data.order !== undefined && (data.order < 0 || data.order > 999)) {
        errors.push({
          field: 'order',
          message: '排序权重必须在0-999之间',
          code: 'INVALID_ORDER'
        })
      }

      return {
        valid: errors.length === 0,
        errors
      }
    }, 'CATEGORY_VALIDATION_ERROR')
  }

  /**
   * 验证金额格式
   */
  public validateAmount(amount: string | number): ServiceResponse<ValidationResult> {
    return this.safeExecuteSync(() => {
      const errors: ValidationError[] = []
      const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount

      if (isNaN(numAmount)) {
        errors.push({
          field: 'amount',
          message: '金额格式无效',
          code: 'INVALID_AMOUNT_FORMAT'
        })
      } else if (numAmount <= 0) {
        errors.push({
          field: 'amount',
          message: '金额必须大于0',
          code: 'AMOUNT_TOO_SMALL'
        })
      } else if (numAmount > 999999999) {
        errors.push({
          field: 'amount',
          message: '金额不能超过999,999,999',
          code: 'AMOUNT_TOO_LARGE'
        })
      }

      return {
        valid: errors.length === 0,
        errors
      }
    }, 'AMOUNT_VALIDATION_ERROR')
  }

  /**
   * 验证日期格式 (YYYY-MM-DD)
   */
  public validateDate(date: string): ServiceResponse<ValidationResult> {
    return this.safeExecuteSync(() => {
      const errors: ValidationError[] = []

      if (!this.isValidDate(date)) {
        errors.push({
          field: 'date',
          message: '日期格式必须为YYYY-MM-DD',
          code: 'INVALID_DATE_FORMAT'
        })
      } else {
        const dateObj = new Date(date)
        const now = new Date()
        const maxDate = new Date(now.getFullYear() + 10, 11, 31) // 10年后

        if (dateObj > maxDate) {
          errors.push({
            field: 'date',
            message: '日期不能超过10年后',
            code: 'DATE_TOO_FUTURE'
          })
        }
      }

      return {
        valid: errors.length === 0,
        errors
      }
    }, 'DATE_VALIDATION_ERROR')
  }

  /**
   * 验证字符串长度
   */
  public validateStringLength(
    value: string,
    minLength: number = 0,
    maxLength: number = 255,
    fieldName: string = 'field'
  ): ServiceResponse<ValidationResult> {
    return this.safeExecuteSync(() => {
      const errors: ValidationError[] = []

      if (value.length < minLength) {
        errors.push({
          field: fieldName,
          message: `${fieldName}长度不能少于${minLength}个字符`,
          code: 'STRING_TOO_SHORT'
        })
      }

      if (value.length > maxLength) {
        errors.push({
          field: fieldName,
          message: `${fieldName}长度不能超过${maxLength}个字符`,
          code: 'STRING_TOO_LONG'
        })
      }

      return {
        valid: errors.length === 0,
        errors
      }
    }, 'STRING_LENGTH_VALIDATION_ERROR')
  }

  /**
   * 检查日期格式是否有效
   */
  private isValidDate(dateString: string): boolean {
    const regex = /^\d{4}-\d{2}-\d{2}$/
    if (!regex.test(dateString)) {
      return false
    }

    const date = new Date(dateString)
    return date instanceof Date && !isNaN(date.getTime()) && 
           date.toISOString().slice(0, 10) === dateString
  }

  /**
   * 检查十六进制颜色格式是否有效
   */
  private isValidHexColor(color: string): boolean {
    const regex = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/
    return regex.test(color)
  }
}

// 导出单例实例
export const validationService = ValidationService.getInstance()
