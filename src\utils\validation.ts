/**
 * 验证工具函数
 * Validation utility functions
 */

import type { Transaction, Category, TransactionType } from '@/types'

// ==========================================================================
// 基础验证 - Basic Validation
// ==========================================================================

/**
 * 检查值是否为空
 * @param value 要检查的值
 * @returns 是否为空
 */
export function isEmpty(value: any): boolean {
  if (value === null || value === undefined) return true
  if (typeof value === 'string') return value.trim().length === 0
  if (Array.isArray(value)) return value.length === 0
  if (typeof value === 'object') return Object.keys(value).length === 0
  return false
}

/**
 * 检查值是否不为空
 * @param value 要检查的值
 * @returns 是否不为空
 */
export function isNotEmpty(value: any): boolean {
  return !isEmpty(value)
}

/**
 * 检查是否为有效数字
 * @param value 要检查的值
 * @returns 是否为有效数字
 */
export function isValidNumber(value: any): boolean {
  return typeof value === 'number' && !isNaN(value) && isFinite(value)
}

/**
 * 检查是否为正数
 * @param value 要检查的值
 * @returns 是否为正数
 */
export function isPositiveNumber(value: any): boolean {
  return isValidNumber(value) && value > 0
}

/**
 * 检查是否为非负数
 * @param value 要检查的值
 * @returns 是否为非负数
 */
export function isNonNegativeNumber(value: any): boolean {
  return isValidNumber(value) && value >= 0
}

/**
 * 检查是否为整数
 * @param value 要检查的值
 * @returns 是否为整数
 */
export function isInteger(value: any): boolean {
  return isValidNumber(value) && Number.isInteger(value)
}

/**
 * 检查数字是否在指定范围内
 * @param value 要检查的值
 * @param min 最小值
 * @param max 最大值
 * @returns 是否在范围内
 */
export function isInRange(value: number, min: number, max: number): boolean {
  return isValidNumber(value) && value >= min && value <= max
}

// ==========================================================================
// 字符串验证 - String Validation
// ==========================================================================

/**
 * 检查字符串长度是否在指定范围内
 * @param str 字符串
 * @param min 最小长度
 * @param max 最大长度
 * @returns 是否在范围内
 */
export function isValidLength(str: string, min: number, max: number): boolean {
  if (typeof str !== 'string') return false
  const length = str.trim().length
  return length >= min && length <= max
}

/**
 * 检查是否为有效的十六进制颜色值
 * @param color 颜色值
 * @returns 是否为有效颜色值
 */
export function isValidHexColor(color: string): boolean {
  if (typeof color !== 'string') return false
  return /^#[0-9A-Fa-f]{6}$/.test(color)
}

/**
 * 检查是否为有效的URL
 * @param url URL字符串
 * @returns 是否为有效URL
 */
export function isValidUrl(url: string): boolean {
  if (typeof url !== 'string') return false
  try {
    new URL(url)
    return true
  } catch {
    return false
  }
}

/**
 * 检查是否为有效的邮箱地址
 * @param email 邮箱地址
 * @returns 是否为有效邮箱
 */
export function isValidEmail(email: string): boolean {
  if (typeof email !== 'string') return false
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

/**
 * 检查是否为有效的手机号码（中国大陆）
 * @param phone 手机号码
 * @returns 是否为有效手机号
 */
export function isValidPhone(phone: string): boolean {
  if (typeof phone !== 'string') return false
  const phoneRegex = /^1[3-9]\d{9}$/
  return phoneRegex.test(phone)
}

// ==========================================================================
// 日期验证 - Date Validation
// ==========================================================================

/**
 * 检查是否为有效日期
 * @param date 日期字符串或Date对象
 * @returns 是否为有效日期
 */
export function isValidDate(date: string | Date): boolean {
  const dateObj = typeof date === 'string' ? new Date(date) : date
  return dateObj instanceof Date && !isNaN(dateObj.getTime())
}

/**
 * 检查日期是否为今天
 * @param date 日期字符串或Date对象
 * @returns 是否为今天
 */
export function isToday(date: string | Date): boolean {
  if (!isValidDate(date)) return false
  
  const dateObj = typeof date === 'string' ? new Date(date) : date
  const today = new Date()
  
  return dateObj.getFullYear() === today.getFullYear() &&
         dateObj.getMonth() === today.getMonth() &&
         dateObj.getDate() === today.getDate()
}

/**
 * 检查日期是否在指定范围内
 * @param date 要检查的日期
 * @param startDate 开始日期
 * @param endDate 结束日期
 * @returns 是否在范围内
 */
export function isDateInRange(
  date: string | Date,
  startDate: string | Date,
  endDate: string | Date
): boolean {
  if (!isValidDate(date) || !isValidDate(startDate) || !isValidDate(endDate)) {
    return false
  }

  const dateObj = typeof date === 'string' ? new Date(date) : date
  const startObj = typeof startDate === 'string' ? new Date(startDate) : startDate
  const endObj = typeof endDate === 'string' ? new Date(endDate) : endDate

  return dateObj >= startObj && dateObj <= endObj
}

/**
 * 检查日期是否不晚于今天
 * @param date 日期字符串或Date对象
 * @returns 是否不晚于今天
 */
export function isNotFutureDate(date: string | Date): boolean {
  if (!isValidDate(date)) return false
  
  const dateObj = typeof date === 'string' ? new Date(date) : date
  const today = new Date()
  today.setHours(23, 59, 59, 999) // 设置为今天的最后一刻
  
  return dateObj <= today
}

// ==========================================================================
// 业务数据验证 - Business Data Validation
// ==========================================================================

/**
 * 验证交易金额
 * @param amount 金额（分为单位）
 * @returns 验证结果
 */
export function validateTransactionAmount(amount: any): {
  valid: boolean
  error?: string
} {
  if (!isValidNumber(amount)) {
    return { valid: false, error: '金额必须是有效数字' }
  }

  if (!isInteger(amount)) {
    return { valid: false, error: '金额必须是整数（分为单位）' }
  }

  if (!isPositiveNumber(amount)) {
    return { valid: false, error: '金额必须大于0' }
  }

  // 检查金额是否过大（最大1亿元）
  if (amount > ***********) {
    return { valid: false, error: '金额不能超过1亿元' }
  }

  return { valid: true }
}

/**
 * 验证交易类型
 * @param type 交易类型
 * @returns 验证结果
 */
export function validateTransactionType(type: any): {
  valid: boolean
  error?: string
} {
  if (typeof type !== 'string') {
    return { valid: false, error: '交易类型必须是字符串' }
  }

  if (!['income', 'expense'].includes(type)) {
    return { valid: false, error: '交易类型必须是income或expense' }
  }

  return { valid: true }
}

/**
 * 验证交易描述
 * @param description 描述
 * @returns 验证结果
 */
export function validateTransactionDescription(description: any): {
  valid: boolean
  error?: string
} {
  if (typeof description !== 'string') {
    return { valid: false, error: '描述必须是字符串' }
  }

  if (!isValidLength(description, 1, 100)) {
    return { valid: false, error: '描述长度必须在1-100个字符之间' }
  }

  return { valid: true }
}

/**
 * 验证分类名称
 * @param name 分类名称
 * @returns 验证结果
 */
export function validateCategoryName(name: any): {
  valid: boolean
  error?: string
} {
  if (typeof name !== 'string') {
    return { valid: false, error: '分类名称必须是字符串' }
  }

  if (isEmpty(name)) {
    return { valid: false, error: '分类名称不能为空' }
  }

  if (!isValidLength(name, 1, 20)) {
    return { valid: false, error: '分类名称长度必须在1-20个字符之间' }
  }

  return { valid: true }
}

/**
 * 验证分类图标
 * @param icon 图标名称
 * @returns 验证结果
 */
export function validateCategoryIcon(icon: any): {
  valid: boolean
  error?: string
} {
  if (typeof icon !== 'string') {
    return { valid: false, error: '图标必须是字符串' }
  }

  if (isEmpty(icon)) {
    return { valid: false, error: '图标不能为空' }
  }

  if (!isValidLength(icon, 1, 50)) {
    return { valid: false, error: '图标名称长度必须在1-50个字符之间' }
  }

  return { valid: true }
}

/**
 * 验证分类颜色
 * @param color 颜色值
 * @returns 验证结果
 */
export function validateCategoryColor(color: any): {
  valid: boolean
  error?: string
} {
  if (typeof color !== 'string') {
    return { valid: false, error: '颜色必须是字符串' }
  }

  if (!isValidHexColor(color)) {
    return { valid: false, error: '颜色必须是有效的十六进制颜色值（如#FF0000）' }
  }

  return { valid: true }
}

/**
 * 验证完整的交易数据
 * @param transaction 交易数据
 * @returns 验证结果
 */
export function validateTransaction(transaction: Partial<Transaction>): {
  valid: boolean
  errors: string[]
} {
  const errors: string[] = []

  // 验证金额
  const amountResult = validateTransactionAmount(transaction.amount)
  if (!amountResult.valid) {
    errors.push(amountResult.error!)
  }

  // 验证类型
  const typeResult = validateTransactionType(transaction.type)
  if (!typeResult.valid) {
    errors.push(typeResult.error!)
  }

  // 验证描述
  if (transaction.description !== undefined) {
    const descResult = validateTransactionDescription(transaction.description)
    if (!descResult.valid) {
      errors.push(descResult.error!)
    }
  }

  // 验证日期
  if (transaction.date) {
    if (!isValidDate(transaction.date)) {
      errors.push('日期格式无效')
    } else if (!isNotFutureDate(transaction.date)) {
      errors.push('日期不能晚于今天')
    }
  }

  // 验证分类ID
  if (transaction.categoryId) {
    if (typeof transaction.categoryId !== 'string' || isEmpty(transaction.categoryId)) {
      errors.push('分类ID无效')
    }
  }

  return {
    valid: errors.length === 0,
    errors
  }
}

/**
 * 验证完整的分类数据
 * @param category 分类数据
 * @returns 验证结果
 */
export function validateCategory(category: Partial<Category>): {
  valid: boolean
  errors: string[]
} {
  const errors: string[] = []

  // 验证名称
  const nameResult = validateCategoryName(category.name)
  if (!nameResult.valid) {
    errors.push(nameResult.error!)
  }

  // 验证类型
  const typeResult = validateTransactionType(category.type)
  if (!typeResult.valid) {
    errors.push(typeResult.error!)
  }

  // 验证图标
  const iconResult = validateCategoryIcon(category.icon)
  if (!iconResult.valid) {
    errors.push(iconResult.error!)
  }

  // 验证颜色
  const colorResult = validateCategoryColor(category.color)
  if (!colorResult.valid) {
    errors.push(colorResult.error!)
  }

  // 验证排序权重
  if (category.order !== undefined) {
    if (!isInteger(category.order) || !isNonNegativeNumber(category.order)) {
      errors.push('排序权重必须是非负整数')
    }
  }

  return {
    valid: errors.length === 0,
    errors
  }
}

// ==========================================================================
// 表单验证 - Form Validation
// ==========================================================================

/**
 * 验证表单字段
 * @param value 字段值
 * @param rules 验证规则
 * @returns 验证结果
 */
export function validateField(value: any, rules: {
  required?: boolean
  type?: 'string' | 'number' | 'email' | 'phone' | 'url' | 'date'
  min?: number
  max?: number
  minLength?: number
  maxLength?: number
  pattern?: RegExp
  custom?: (value: any) => boolean | string
}): {
  valid: boolean
  error?: string
} {
  // 必填验证
  if (rules.required && isEmpty(value)) {
    return { valid: false, error: '此字段为必填项' }
  }

  // 如果值为空且非必填，则通过验证
  if (isEmpty(value) && !rules.required) {
    return { valid: true }
  }

  // 类型验证
  if (rules.type) {
    switch (rules.type) {
      case 'string':
        if (typeof value !== 'string') {
          return { valid: false, error: '必须是字符串' }
        }
        break
      case 'number':
        if (!isValidNumber(value)) {
          return { valid: false, error: '必须是有效数字' }
        }
        break
      case 'email':
        if (!isValidEmail(value)) {
          return { valid: false, error: '邮箱格式无效' }
        }
        break
      case 'phone':
        if (!isValidPhone(value)) {
          return { valid: false, error: '手机号格式无效' }
        }
        break
      case 'url':
        if (!isValidUrl(value)) {
          return { valid: false, error: 'URL格式无效' }
        }
        break
      case 'date':
        if (!isValidDate(value)) {
          return { valid: false, error: '日期格式无效' }
        }
        break
    }
  }

  // 数值范围验证
  if (rules.type === 'number' && isValidNumber(value)) {
    if (rules.min !== undefined && value < rules.min) {
      return { valid: false, error: `值不能小于${rules.min}` }
    }
    if (rules.max !== undefined && value > rules.max) {
      return { valid: false, error: `值不能大于${rules.max}` }
    }
  }

  // 字符串长度验证
  if (rules.type === 'string' && typeof value === 'string') {
    if (rules.minLength !== undefined && value.length < rules.minLength) {
      return { valid: false, error: `长度不能少于${rules.minLength}个字符` }
    }
    if (rules.maxLength !== undefined && value.length > rules.maxLength) {
      return { valid: false, error: `长度不能超过${rules.maxLength}个字符` }
    }
  }

  // 正则表达式验证
  if (rules.pattern && typeof value === 'string') {
    if (!rules.pattern.test(value)) {
      return { valid: false, error: '格式不正确' }
    }
  }

  // 自定义验证
  if (rules.custom) {
    const result = rules.custom(value)
    if (typeof result === 'string') {
      return { valid: false, error: result }
    }
    if (!result) {
      return { valid: false, error: '验证失败' }
    }
  }

  return { valid: true }
}
