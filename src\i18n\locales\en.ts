export default {
  // App title
  app: {
    name: 'Money Tracker'
  },
  
  // Navigation
  nav: {
    track: 'Accounting',
    stats: 'Stats',
    settings: 'Settings'
  },
  
  // Home page
  home: {
    title: 'Track',
    monthlyIncome: 'Monthly Income',
    monthlyExpense: 'Monthly Expense',
    expenseButton: 'Expense Button',
    incomeButton: 'Income Button',
    recentTransactions: 'Recent Transactions',
    noTransactions: 'No transactions yet',
    addTransaction: 'Add Transaction',
    addExpense: 'Add Expense',
    addIncome: 'Add Income',
    expense: 'Expense',
    income: 'Income',
    amount: 'Amount',
    category: 'Category',
    note: 'Note',
    date: 'Date',
    submit: 'Submit',
    cancel: 'Cancel',
    done: 'Done',
    selectCategory: 'Select Category',
    today: 'Today',
    yesterday: 'Yesterday',
    daysAgo: 'days ago',
    noNote: 'No note'
  },
  
  // Stats page
  stats: {
    title: 'Statistics',
    expense: 'Expense',
    income: 'Income',
    balance: 'Balance',
    expenseCategories: 'Expense Categories',
    incomeCategories: 'Income Categories',
    noData: 'No data available',
    thisMonth: 'This Month',
    lastMonth: 'Last Month',
    thisYear: 'This Year',
    category: 'Category',
    amount: 'Amount',
    percentage: 'Percentage',
    noTransactions: 'No transactions this month',
    unknownCategory: 'Unknown category',
    transactions: 'Transactions'
  },
  
  // Settings page
  settings: {
    title: 'Settings',
    appearance: 'Appearance',
    data: 'Data',
    about: 'About',
    theme: 'Theme',
    language: 'Language',
    backup: 'Backup',
    export: 'Export Data',
    exportDescription: 'Export all transaction records',
    import: 'Import Data',
    importDescription: 'Restore data from backup file',
    clear: 'Clear All Data',
    clearDescription: 'Delete all transaction records and settings',
    light: 'Light Mode',
    dark: 'Dark Mode',
    system: 'Follow System',
    chinese: '中文',
    english: 'English',
    selectTheme: 'Select Theme',
    version: 'Version',
    confirmClear: 'Confirm to clear all data?',
    confirmClearTitle: 'Confirm Clear Data',
    confirmClearMessage: 'This operation will delete all transaction records and settings, and cannot be undone. Are you sure you want to continue?',
    confirmClear: 'Confirm Clear',
    followSystem: 'Follow System',
    fixed: 'Fixed',
    confirmImportTitle: 'Confirm Import Data',
    confirmImportMessage: 'This operation will overwrite all existing data, including transaction records and settings. Are you sure you want to continue?',
    confirmImport: 'Confirm Import',
    clearSuccess: 'Data cleared successfully',
    exportSuccess: 'Data exported successfully',
    importSuccess: 'Data imported successfully',
    importFormatError: 'Invalid file format, please select a valid backup file',
    importParseError: 'Failed to parse file, please check the file format',
    importFailed: 'Data import failed, please try again',
    importFileInfo: 'File Information',
    transactions: 'Transactions',
    categories: 'Categories',
    exportDate: 'Export Date',
    settings: 'Settings',
    currentDataWarning: 'Import will overwrite all existing data, including current transactions, categories, and settings.'
  },

  // Backup page
  backup: {
    title: 'Data Backup',
    settings: 'Backup Settings',
    autoBackup: 'Auto Backup',
    autoBackupDesc: 'Automatically backup data to local storage',
    frequency: 'Backup Frequency',
    frequencyDesc: 'Backup every {{days}} days',
    lastBackup: 'Last backup: {{time}}',
    manual: 'Manual Backup',
    manualDesc: 'Export all your data including transaction records and category settings.',
    exportData: 'Export Data',
    restore: 'Data Recovery',
    restoreDesc: 'Restore your data from backup file. Note: This will overwrite all current data.',
    selectFile: 'Select Backup File',
    restoreData: 'Restore Data',
    dangerZone: 'Danger Zone',
    dangerDesc: 'Clear all data including transaction records and custom categories. This action cannot be undone.',
    clearAll: 'Clear All Data',
    confirmClearTitle: 'Confirm Clear Data',
    confirmClearDesc: 'Are you sure you want to clear all data? This will delete all transaction records and custom categories, and this action cannot be undone.',
    exportFailed: 'Export failed, please try again',
    restoreSuccess: 'Data restored successfully!',
    restoreFormatError: 'Data format is incorrect, please check the backup file',
    restoreFailed: 'Data restore failed, please check the file format',
    clearAllSuccess: 'All data has been cleared',
    daily: 'Daily',
    every3Days: 'Every 3 days',
    weekly: 'Weekly',
    monthly: 'Monthly'
  },
  
  // Categories
  categories: {
    food: 'Food',
    transport: 'Transport',
    shopping: 'Shopping',
    entertainment: 'Entertainment',
    health: 'Health',
    education: 'Education',
    housing: 'Housing',
    other: 'Other',
    salary: 'Salary',
    bonus: 'Bonus',
    investment: 'Investment',
    gift: 'Gift'
  },



  // Common
  common: {
    confirm: 'Confirm',
    cancel: 'Cancel',
    save: 'Save',
    delete: 'Delete',
    edit: 'Edit',
    loading: 'Loading...',
    error: 'Error',
    success: 'Success',
    back: 'Back',
    close: 'Close',
    unknown: 'Unknown',
    included: 'Included'
  }
}
