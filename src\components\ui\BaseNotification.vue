<template>
  <Teleport to="body">
    <div
      v-if="visible"
      class="notification-container"
      :class="containerClasses"
    >
      <Transition
        name="notification"
        appear
        @enter="onEnter"
        @leave="onLeave"
      >
        <div
          v-if="show"
          class="notification"
          :class="notificationClasses"
          :role="role"
          :aria-live="ariaLive"
          @click="handleClick"
          @mouseenter="handleMouseEnter"
          @mouseleave="handleMouseLeave"
        >
          <!-- 图标 -->
          <div class="notification-icon" :class="iconClasses">
            <component 
              v-if="customIcon" 
              :is="customIcon" 
              class="w-5 h-5" 
            />
            <template v-else>
              <!-- Success Icon -->
              <svg v-if="type === 'success'" class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
              </svg>
              <!-- Error Icon -->
              <svg v-else-if="type === 'error'" class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
              <!-- Warning Icon -->
              <svg v-else-if="type === 'warning'" class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
              <!-- Info Icon -->
              <svg v-else class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </template>
          </div>
          
          <!-- 内容 -->
          <div class="notification-content">
            <div v-if="title" class="notification-title">
              {{ title }}
            </div>
            <div v-if="message" class="notification-message">
              {{ message }}
            </div>
            <slot />
          </div>
          
          <!-- 操作按钮 -->
          <div v-if="actions && actions.length > 0" class="notification-actions">
            <button
              v-for="action in actions"
              :key="action.label"
              type="button"
              class="notification-action"
              :class="getActionClasses(action)"
              @click="handleActionClick(action)"
            >
              {{ action.label }}
            </button>
          </div>
          
          <!-- 关闭按钮 -->
          <button
            v-if="closable"
            type="button"
            class="notification-close"
            :aria-label="closeAriaLabel"
            @click="handleClose"
          >
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
          
          <!-- 进度条 -->
          <div
            v-if="showProgress && duration > 0"
            class="notification-progress"
          >
            <div
              class="notification-progress-bar"
              :class="progressClasses"
              :style="progressStyle"
            ></div>
          </div>
        </div>
      </Transition>
    </div>
  </Teleport>
</template>

<script setup lang="ts">
import { computed, ref, onMounted, onUnmounted, watch } from 'vue'
import type { Component } from 'vue'
import type { NotificationProps, NotificationAction } from '@/types/components'

interface Props extends /* @vue-ignore */ NotificationProps {
  /** 自定义图标 */
  customIcon?: string | Component
  /** 是否显示进度条 */
  showProgress?: boolean
  /** 关闭按钮无障碍标签 */
  closeAriaLabel?: string
}

const props = withDefaults(defineProps<Props>(), {
  type: 'info',
  position: 'top-right',
  duration: 4000,
  closable: true,
  persistent: false,
  showProgress: false,
  closeAriaLabel: '关闭通知'
})

const emit = defineEmits<{
  close: []
  click: []
  action: [action: NotificationAction]
}>()

// 响应式数据
const visible = ref(false)
const show = ref(false)
const isPaused = ref(false)
const remainingTime = ref(0)
const progressWidth = ref(100)
const timer = ref<number>()
const progressTimer = ref<number>()

// 计算属性
const role = computed(() => {
  return props.type === 'error' ? 'alert' : 'status'
})

const ariaLive = computed(() => {
  return props.type === 'error' ? 'assertive' : 'polite'
})

const containerClasses = computed(() => [
  'notification-container',
  `notification-container-${props.position}`
])

const notificationClasses = computed(() => [
  'notification',
  `notification-${props.type}`,
  {
    'notification-clickable': !!$slots.default || !!props.message,
    'notification-with-actions': props.actions && props.actions.length > 0
  }
])

const iconClasses = computed(() => [
  'notification-icon',
  `notification-icon-${props.type}`
])

const progressClasses = computed(() => [
  'notification-progress-bar',
  `notification-progress-${props.type}`
])

const progressStyle = computed(() => ({
  width: `${progressWidth.value}%`,
  transition: isPaused.value ? 'none' : `width ${props.duration}ms linear`
}))

// 方法
const open = () => {
  visible.value = true
  show.value = true
  
  if (props.duration > 0 && !props.persistent) {
    startTimer()
  }
}

const close = () => {
  show.value = false
  emit('close')
}

const startTimer = () => {
  if (props.duration <= 0) return
  
  remainingTime.value = props.duration
  
  if (props.showProgress) {
    startProgressTimer()
  }
  
  timer.value = window.setTimeout(() => {
    close()
  }, props.duration)
}

const startProgressTimer = () => {
  const startTime = Date.now()
  const updateProgress = () => {
    if (isPaused.value) {
      progressTimer.value = window.requestAnimationFrame(updateProgress)
      return
    }
    
    const elapsed = Date.now() - startTime
    const remaining = Math.max(0, props.duration - elapsed)
    progressWidth.value = (remaining / props.duration) * 100
    
    if (remaining > 0) {
      progressTimer.value = window.requestAnimationFrame(updateProgress)
    }
  }
  
  progressTimer.value = window.requestAnimationFrame(updateProgress)
}

const pauseTimer = () => {
  if (timer.value) {
    clearTimeout(timer.value)
    isPaused.value = true
  }
}

const resumeTimer = () => {
  if (remainingTime.value > 0 && !props.persistent) {
    isPaused.value = false
    timer.value = window.setTimeout(() => {
      close()
    }, remainingTime.value)
  }
}

const getActionClasses = (action: NotificationAction) => [
  'notification-action',
  `notification-action-${action.type || 'default'}`
]

// 事件处理
const handleClick = () => {
  emit('click')
}

const handleClose = () => {
  close()
}

const handleActionClick = (action: NotificationAction) => {
  emit('action', action)
  if (action.handler) {
    action.handler()
  }
  if (action.closeOnClick !== false) {
    close()
  }
}

const handleMouseEnter = () => {
  if (props.duration > 0 && !props.persistent) {
    pauseTimer()
  }
}

const handleMouseLeave = () => {
  if (props.duration > 0 && !props.persistent) {
    resumeTimer()
  }
}

const onEnter = () => {
  // 进入动画完成
}

const onLeave = () => {
  visible.value = false
}

// 监听器
watch(() => props.duration, (newDuration) => {
  if (newDuration > 0 && !props.persistent && show.value) {
    if (timer.value) {
      clearTimeout(timer.value)
    }
    startTimer()
  }
})

// 生命周期
onMounted(() => {
  open()
})

onUnmounted(() => {
  if (timer.value) {
    clearTimeout(timer.value)
  }
  if (progressTimer.value) {
    cancelAnimationFrame(progressTimer.value)
  }
})

// 暴露方法
defineExpose({
  open,
  close,
  pause: pauseTimer,
  resume: resumeTimer
})
</script>

<style scoped>
/* 通知容器 */
.notification-container {
  @apply fixed z-50 pointer-events-none;
}

.notification-container-top-left {
  @apply top-4 left-4;
}

.notification-container-top-center {
  @apply top-4 left-1/2 transform -translate-x-1/2;
}

.notification-container-top-right {
  @apply top-4 right-4;
}

.notification-container-bottom-left {
  @apply bottom-4 left-4;
}

.notification-container-bottom-center {
  @apply bottom-4 left-1/2 transform -translate-x-1/2;
}

.notification-container-bottom-right {
  @apply bottom-4 right-4;
}

/* 通知主体 */
.notification {
  @apply relative flex items-start p-4 rounded-lg shadow-lg pointer-events-auto;
  @apply bg-white border border-gray-200 dark:bg-gray-800 dark:border-gray-600;
  @apply max-w-sm w-full;
}

.notification-clickable {
  @apply cursor-pointer;
}

.notification-with-actions {
  @apply pb-2;
}

/* 通知类型样式 */
.notification-success {
  @apply border-l-4 border-l-green-500;
}

.notification-error {
  @apply border-l-4 border-l-red-500;
}

.notification-warning {
  @apply border-l-4 border-l-yellow-500;
}

.notification-info {
  @apply border-l-4 border-l-blue-500;
}

/* 图标样式 */
.notification-icon {
  @apply flex-shrink-0 mr-3;
}

.notification-icon-success {
  @apply text-green-500;
}

.notification-icon-error {
  @apply text-red-500;
}

.notification-icon-warning {
  @apply text-yellow-500;
}

.notification-icon-info {
  @apply text-blue-500;
}

/* 内容样式 */
.notification-content {
  @apply flex-1 min-w-0;
}

.notification-title {
  @apply text-sm font-medium text-gray-900 dark:text-gray-100;
}

.notification-message {
  @apply mt-1 text-sm text-gray-600 dark:text-gray-300;
}

/* 操作按钮 */
.notification-actions {
  @apply flex space-x-2 mt-3;
}

.notification-action {
  @apply px-3 py-1 text-xs font-medium rounded transition-colors;
  @apply focus:outline-none focus:ring-2 focus:ring-offset-2;
}

.notification-action-default {
  @apply bg-gray-100 text-gray-700 hover:bg-gray-200;
  @apply dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600;
  @apply focus:ring-gray-500;
}

.notification-action-primary {
  @apply bg-blue-100 text-blue-700 hover:bg-blue-200;
  @apply dark:bg-blue-900 dark:text-blue-300 dark:hover:bg-blue-800;
  @apply focus:ring-blue-500;
}

/* 关闭按钮 */
.notification-close {
  @apply ml-4 flex-shrink-0 text-gray-400 hover:text-gray-600;
  @apply dark:text-gray-500 dark:hover:text-gray-300;
  @apply focus:outline-none focus:ring-2 focus:ring-gray-500 rounded;
}

/* 进度条 */
.notification-progress {
  @apply absolute bottom-0 left-0 right-0 h-1 bg-gray-200 dark:bg-gray-700;
}

.notification-progress-bar {
  @apply h-full;
}

.notification-progress-success {
  @apply bg-green-500;
}

.notification-progress-error {
  @apply bg-red-500;
}

.notification-progress-warning {
  @apply bg-yellow-500;
}

.notification-progress-info {
  @apply bg-blue-500;
}

/* 动画 */
.notification-enter-active,
.notification-leave-active {
  @apply transition-all duration-300 ease-out;
}

.notification-enter-from {
  @apply opacity-0 transform translate-x-full;
}

.notification-leave-to {
  @apply opacity-0 transform translate-x-full;
}

/* 响应式 */
@media (max-width: 640px) {
  .notification {
    @apply max-w-none mx-4;
  }
}
</style>
