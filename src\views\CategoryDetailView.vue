<template>
  <div class="page-container">
    <!-- 头部 -->
    <div class="sticky top-0 bg-gray-50 dark:bg-black border-b border-gray-200 dark:border-gray-800 px-4 py-4 safe-area-top">
      <div class="flex items-center justify-between">
        <button
          @click="goBack"
          class="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
        >
          <ArrowLeftIcon class="w-6 h-6" />
        </button>
        <h1 class="text-lg font-semibold">分类详情</h1>
        <button
          v-if="category && !category.isDefault"
          @click="showEditModal = true"
          class="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
        >
          <PencilIcon class="w-6 h-6" />
        </button>
        <div v-else class="w-10"></div>
      </div>
    </div>

    <!-- 内容 -->
    <div v-if="category" class="p-4 space-y-6">
      <!-- 分类信息卡片 -->
      <BaseCard>
        <div class="text-center py-8">
          <div
            class="w-20 h-20 mx-auto mb-4 rounded-full flex items-center justify-center text-4xl"
            :style="{ backgroundColor: category.color + '20' }"
          >
            {{ category.icon }}
          </div>
          <h2 class="text-2xl font-bold mb-2">{{ category.name }}</h2>
          <div class="inline-flex items-center px-3 py-1 rounded-full text-sm" :class="[
            category.type === 'income' 
              ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
              : 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400'
          ]">
            {{ category.type === 'income' ? t('home.income') : t('home.expense') }}
          </div>
        </div>
      </BaseCard>

      <!-- 统计信息 -->
      <BaseCard>
        <template #header>
          <h3 class="text-lg font-semibold">统计信息</h3>
        </template>
        
        <div class="grid grid-cols-2 gap-4">
          <div class="text-center">
            <p class="text-2xl font-bold" :class="[
              category.type === 'income' ? 'text-green-500' : 'text-red-500'
            ]">
              {{ formatCurrency(totalAmount) }}
            </p>
            <p class="text-sm text-gray-500 dark:text-gray-400">总金额</p>
          </div>
          <div class="text-center">
            <p class="text-2xl font-bold text-gray-900 dark:text-gray-100">
              {{ transactionCount }}
            </p>
            <p class="text-sm text-gray-500 dark:text-gray-400">交易笔数</p>
          </div>
        </div>
      </BaseCard>

      <!-- 最近交易 -->
      <BaseCard>
        <template #header>
          <div class="flex items-center justify-between">
            <h3 class="text-lg font-semibold">最近交易</h3>
            <span class="text-sm text-gray-500 dark:text-gray-400">
              共 {{ categoryTransactions.length }} 笔
            </span>
          </div>
        </template>
        
        <div v-if="categoryTransactions.length > 0" class="space-y-3">
          <div
            v-for="transaction in recentCategoryTransactions"
            :key="transaction.id"
            class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-2xl cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
            @click="showTransactionDetail(transaction)"
          >
            <div class="flex-1">
              <p class="font-medium text-gray-900 dark:text-gray-100">
                {{ transaction.description || '无备注' }}
              </p>
              <p class="text-sm text-gray-500 dark:text-gray-400">
                {{ formatDate(transaction.date) }}
              </p>
            </div>
            <div class="text-right">
              <p class="font-semibold" :class="[
                transaction.type === 'income' ? 'text-green-500' : 'text-red-500'
              ]">
                {{ transaction.type === 'income' ? '+' : '-' }}{{ formatCurrency(transaction.amount) }}
              </p>
            </div>
          </div>
          
          <div v-if="categoryTransactions.length > 5" class="text-center">
            <button
              @click="showAllTransactions = !showAllTransactions"
              class="text-blue-500 hover:text-blue-600 text-sm"
            >
              {{ showAllTransactions ? '收起' : `查看全部 ${categoryTransactions.length} 笔交易` }}
            </button>
          </div>
        </div>
        
        <div v-else class="text-center py-8">
          <div class="w-16 h-16 mx-auto mb-4 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center">
            <DocumentTextIcon class="w-8 h-8 text-gray-400" />
          </div>
          <p class="text-gray-500 dark:text-gray-400">暂无交易记录</p>
        </div>
      </BaseCard>

      <!-- 操作按钮 -->
      <div v-if="!category.isDefault" class="space-y-3">
        <BaseButton
          variant="secondary"
          full-width
          @click="showEditModal = true"
        >
          <PencilIcon class="w-5 h-5 mr-2" />
          编辑分类
        </BaseButton>
        
        <BaseButton
          variant="danger"
          full-width
          @click="showDeleteConfirm = true"
          :disabled="categoryTransactions.length > 0"
        >
          <TrashIcon class="w-5 h-5 mr-2" />
          删除分类
        </BaseButton>
        
        <p v-if="categoryTransactions.length > 0" class="text-sm text-gray-500 dark:text-gray-400 text-center">
          有交易记录的分类无法删除
        </p>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-else class="flex items-center justify-center min-h-screen">
      <div class="text-center">
        <div class="animate-spin w-8 h-8 border-2 border-blue-500 border-t-transparent rounded-full mx-auto mb-4"></div>
        <p class="text-gray-500 dark:text-gray-400">加载中...</p>
      </div>
    </div>

    <!-- 编辑模态框 -->
    <BaseModal
      v-model="showEditModal"
      title="编辑分类"
      position="bottom"
    >
      <p class="text-center text-gray-500 dark:text-gray-400 py-8">
        编辑功能开发中...
      </p>
    </BaseModal>

    <!-- 删除确认模态框 -->
    <BaseModal
      v-model="showDeleteConfirm"
      title="确认删除"
      size="sm"
    >
      <div class="text-center py-4">
        <div class="w-16 h-16 mx-auto mb-4 bg-red-100 dark:bg-red-900/20 rounded-full flex items-center justify-center">
          <ExclamationTriangleIcon class="w-8 h-8 text-red-500" />
        </div>
        <p class="text-gray-600 dark:text-gray-400 mb-6">
          确定要删除分类"{{ category?.name }}"吗？此操作无法撤销。
        </p>
        <div class="flex space-x-3">
          <BaseButton
            variant="secondary"
            full-width
            @click="showDeleteConfirm = false"
          >
            取消
          </BaseButton>
          <BaseButton
            variant="danger"
            full-width
            @click="deleteCategory"
          >
            删除
          </BaseButton>
        </div>
      </div>
    </BaseModal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import {
  ArrowLeftIcon,
  PencilIcon,
  TrashIcon,
  DocumentTextIcon,
  ExclamationTriangleIcon
} from '@heroicons/vue/24/outline'
import { BaseButton, BaseCard, BaseModal } from '@/components/ui'
import { useTransactionStore } from '@/stores/transactions'
import { useSettingsStore } from '@/stores/settings'

const route = useRoute()
const router = useRouter()
const transactionStore = useTransactionStore()
const settingsStore = useSettingsStore()
const { t, locale } = useI18n()

const showEditModal = ref(false)
const showDeleteConfirm = ref(false)
const showAllTransactions = ref(false)

const categoryId = computed(() => route.params.id as string)

const category = computed(() => 
  transactionStore.getCategoryById(categoryId.value)
)

const categoryTransactions = computed(() => 
  transactionStore.getTransactionsByCategory(categoryId.value)
)

const recentCategoryTransactions = computed(() => {
  const transactions = categoryTransactions.value
    .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
  
  return showAllTransactions.value ? transactions : transactions.slice(0, 5)
})

const totalAmount = computed(() => 
  categoryTransactions.value.reduce((sum, t) => sum + t.amount, 0)
)

const transactionCount = computed(() => categoryTransactions.value.length)

const formatCurrency = (amount: number) => settingsStore.formatCurrency(amount)

const formatDate = (dateString: string) => {
  const date = new Date(dateString)
  const now = new Date()
  const diffTime = now.getTime() - date.getTime()
  const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24))

  if (diffDays === 0) {
    return t('home.today')
  } else if (diffDays === 1) {
    return t('home.yesterday')
  } else if (diffDays < 7) {
    return `${diffDays}${t('home.daysAgo')}`
  } else {
    const localeMap = {
      'zh': 'zh-CN',
      'en': 'en-US'
    }
    const currentLocale = localeMap[locale.value as keyof typeof localeMap] || 'en-US'
    return date.toLocaleDateString(currentLocale, { month: 'short', day: 'numeric' })
  }
}

const goBack = () => {
  router.back()
}

const showTransactionDetail = (transaction: any) => {
  router.push(`/transaction/${transaction.id}`)
}

const deleteCategory = () => {
  if (category.value) {
    transactionStore.deleteCategory(category.value.id)
    showDeleteConfirm.value = false
    router.push('/settings')
  }
}

onMounted(() => {
  // 如果分类不存在，返回设置页
  if (!category.value) {
    router.push('/settings')
  }
})
</script>
