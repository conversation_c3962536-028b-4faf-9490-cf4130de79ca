/* 导入设计系统 */
@import './design-system.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    -webkit-tap-highlight-color: transparent;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
  }

  body {
    @apply bg-gray-50 dark:bg-black text-gray-900 dark:text-gray-100;
    @apply font-sans antialiased overflow-x-hidden;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  /* 移动端优化 */
  input, textarea, select {
    @apply text-base; /* 防止iOS缩放 */
  }

  /* 滚动条样式 */
  ::-webkit-scrollbar {
    width: 4px;
  }

  ::-webkit-scrollbar-track {
    @apply bg-transparent;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-gray-300 dark:bg-gray-600 rounded-full;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-gray-400 dark:bg-gray-500;
  }

  /* 页面容器 */
  .page-container {
    @apply min-h-screen pb-20; /* 为底部导航留出空间 */
    overflow-x: hidden;
  }

  /* 可滚动区域 */
  .scrollable-area {
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
  }
}


