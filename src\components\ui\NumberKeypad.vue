<template>
  <div class="number-keypad">
    <!-- 显示区域 -->
    <div class="keypad-display">
      <div class="display-amount">
        <span class="currency-symbol">¥</span>
        <span class="amount-text" :class="amountClass">{{ displayAmount }}</span>
      </div>
    </div>

    <!-- 键盘区域 -->
    <div class="keypad-grid">
      <!-- 第一行 -->
      <button
        v-for="num in [1, 2, 3]"
        :key="num"
        @click="inputNumber(num)"
        class="keypad-btn keypad-number"
      >
        {{ num }}
      </button>

      <!-- 第二行 -->
      <button
        v-for="num in [4, 5, 6]"
        :key="num"
        @click="inputNumber(num)"
        class="keypad-btn keypad-number"
      >
        {{ num }}
      </button>

      <!-- 第三行 -->
      <button
        v-for="num in [7, 8, 9]"
        :key="num"
        @click="inputNumber(num)"
        class="keypad-btn keypad-number"
      >
        {{ num }}
      </button>

      <!-- 第四行 -->
      <button
        @click="deleteNumber"
        class="keypad-btn keypad-delete"
      >
        <BackspaceIcon class="w-6 h-6" />
      </button>
      <button
        @click="inputNumber(0)"
        class="keypad-btn keypad-number"
      >
        0
      </button>
      <button
        @click="handleSubmit"
        :disabled="submitDisabled"
        class="keypad-btn keypad-submit"
        :class="submitButtonClass"
      >
        {{ $t('home.done') }}
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue'
import { BackspaceIcon } from '@heroicons/vue/24/outline'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()

interface Props {
  modelValue: string
  type?: 'expense' | 'income'
  maxLength?: number
  submitDisabled?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  type: 'expense',
  maxLength: 8,
  submitDisabled: false
})

const emit = defineEmits<{
  'update:modelValue': [value: string]
  'submit': []
}>()

const amount = ref(props.modelValue || '0')

// 监听外部值变化
watch(() => props.modelValue, (newValue) => {
  amount.value = newValue || '0'
})

// 监听内部值变化并发出事件
watch(amount, (newValue) => {
  emit('update:modelValue', newValue)
})

// 计算属性
const displayAmount = computed(() => {
  if (amount.value === '0' || amount.value === '') {
    return '0'
  }
  return amount.value
})

const amountClass = computed(() => {
  return props.type === 'expense' ? 'text-red-500' : 'text-green-500'
})

const submitButtonClass = computed(() => {
  if (props.submitDisabled) {
    return 'bg-gray-300 dark:bg-gray-700 text-gray-500 dark:text-gray-400 cursor-not-allowed'
  }
  return props.type === 'expense'
    ? 'bg-red-500 hover:bg-red-600 text-white'
    : 'bg-green-500 hover:bg-green-600 text-white'
})

// 方法
function inputNumber(num: number) {
  if (amount.value.length >= props.maxLength) return

  if (amount.value === '0') {
    amount.value = num.toString()
  } else {
    amount.value += num.toString()
  }
}

function deleteNumber() {
  if (amount.value.length <= 1) {
    amount.value = '0'
  } else {
    amount.value = amount.value.slice(0, -1)
  }
}

function handleSubmit() {
  if (!props.submitDisabled) {
    emit('submit')
  }
}

// 重置方法
function reset() {
  amount.value = '0'
}

// 暴露方法给父组件
defineExpose({
  reset
})
</script>

<style scoped>
.number-keypad {
  @apply bg-white dark:bg-black;
  /* 为全面屏机型优化底部间距 */
  padding-bottom: calc(env(safe-area-inset-bottom) + 1rem);
}

.keypad-display {
  @apply px-6 py-8 border-b border-gray-200 dark:border-gray-700;
}

.display-amount {
  @apply flex items-baseline justify-center;
}

.currency-symbol {
  @apply text-2xl text-gray-400 dark:text-gray-500 mr-2;
}

.amount-text {
  @apply text-4xl font-bold;
  font-variant-numeric: tabular-nums;
}

.keypad-grid {
  @apply grid grid-cols-3 gap-px bg-gray-200 dark:bg-gray-700 p-px;
}

.keypad-btn {
  @apply bg-white dark:bg-black text-gray-900 dark:text-gray-100;
  @apply h-16 flex items-center justify-center text-xl font-medium;
  @apply transition-colors duration-150;
  @apply active:bg-gray-100 dark:active:bg-gray-800;
}

.keypad-number:hover {
  @apply bg-gray-50 dark:bg-gray-900;
}

.keypad-delete:hover {
  @apply bg-gray-50 dark:bg-gray-900;
}

.keypad-delete {
  @apply text-gray-500 dark:text-gray-400;
}

.keypad-submit {
  @apply font-semibold text-sm transition-all duration-200;
}

.keypad-submit:not(:disabled):active {
  @apply scale-95;
}

.keypad-submit:disabled {
  @apply cursor-not-allowed;
}
</style>
