<template>
  <div class="page-container">
    <!-- 头部 -->
    <div class="sticky top-0 bg-gray-50 dark:bg-black border-b border-gray-200 dark:border-gray-800 px-4 py-4 safe-area-top">
      <div class="flex items-center justify-between">
        <button
          @click="goBack"
          class="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
        >
          <ArrowLeftIcon class="w-6 h-6" />
        </button>
        <h1 class="text-lg font-semibold">交易详情</h1>
        <button
          @click="showEditModal = true"
          class="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
        >
          <PencilIcon class="w-6 h-6" />
        </button>
      </div>
    </div>

    <!-- 内容 -->
    <div v-if="transaction" class="p-4 space-y-6">
      <!-- 金额卡片 -->
      <BaseCard>
        <div class="text-center py-8">
          <div class="text-4xl font-bold mb-2" :class="[
            transaction.type === 'income' ? 'text-green-500' : 'text-red-500'
          ]">
            {{ transaction.type === 'income' ? '+' : '-' }}{{ formatCurrency(transaction.amount) }}
          </div>
          <div class="text-gray-500 dark:text-gray-400">
            {{ transaction.type === 'income' ? '收入' : '支出' }}
          </div>
        </div>
      </BaseCard>

      <!-- 详细信息 -->
      <BaseCard>
        <div class="space-y-4">
          <div class="flex items-center justify-between">
            <span class="text-gray-500 dark:text-gray-400">分类</span>
            <div class="flex items-center">
              <span class="text-2xl mr-2">{{ category?.icon }}</span>
              <span>{{ category?.name }}</span>
            </div>
          </div>
          
          <div class="flex items-center justify-between">
            <span class="text-gray-500 dark:text-gray-400">描述</span>
            <span>{{ transaction.description || '无' }}</span>
          </div>
          
          <div class="flex items-center justify-between">
            <span class="text-gray-500 dark:text-gray-400">日期</span>
            <span>{{ formatDate(transaction.date) }}</span>
          </div>
          
          <div class="flex items-center justify-between">
            <span class="text-gray-500 dark:text-gray-400">创建时间</span>
            <span>{{ formatDateTime(transaction.createdAt) }}</span>
          </div>
          
          <div v-if="transaction.updatedAt !== transaction.createdAt" class="flex items-center justify-between">
            <span class="text-gray-500 dark:text-gray-400">更新时间</span>
            <span>{{ formatDateTime(transaction.updatedAt) }}</span>
          </div>
        </div>
      </BaseCard>

      <!-- 操作按钮 -->
      <div class="space-y-3">
        <BaseButton
          variant="secondary"
          full-width
          @click="showEditModal = true"
        >
          <PencilIcon class="w-5 h-5 mr-2" />
          编辑交易
        </BaseButton>
        
        <BaseButton
          variant="danger"
          full-width
          @click="showDeleteConfirm = true"
        >
          <TrashIcon class="w-5 h-5 mr-2" />
          删除交易
        </BaseButton>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-else class="flex items-center justify-center min-h-screen">
      <div class="text-center">
        <div class="animate-spin w-8 h-8 border-2 border-blue-500 border-t-transparent rounded-full mx-auto mb-4"></div>
        <p class="text-gray-500 dark:text-gray-400">加载中...</p>
      </div>
    </div>

    <!-- 编辑模态框 -->
    <BaseModal
      v-model="showEditModal"
      title="编辑交易"
      position="bottom"
    >
      <!-- 这里可以放置编辑表单 -->
      <p class="text-center text-gray-500 dark:text-gray-400 py-8">
        编辑功能开发中...
      </p>
    </BaseModal>

    <!-- 删除确认模态框 -->
    <BaseModal
      v-model="showDeleteConfirm"
      title="确认删除"
      size="sm"
    >
      <div class="text-center py-4">
        <p class="text-gray-600 dark:text-gray-400 mb-6">
          确定要删除这条交易记录吗？此操作无法撤销。
        </p>
        <div class="flex space-x-3">
          <BaseButton
            variant="secondary"
            full-width
            @click="showDeleteConfirm = false"
          >
            取消
          </BaseButton>
          <BaseButton
            variant="danger"
            full-width
            @click="deleteTransaction"
          >
            删除
          </BaseButton>
        </div>
      </div>
    </BaseModal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import {
  ArrowLeftIcon,
  PencilIcon,
  TrashIcon
} from '@heroicons/vue/24/outline'
import { BaseButton, BaseCard, BaseModal } from '@/components/ui'
import { useTransactionStore } from '@/stores/transactions'
import { useSettingsStore } from '@/stores/settings'

const route = useRoute()
const router = useRouter()
const transactionStore = useTransactionStore()
const settingsStore = useSettingsStore()

const showEditModal = ref(false)
const showDeleteConfirm = ref(false)

const transactionId = computed(() => route.params.id as string)

const transaction = computed(() => 
  transactionStore.transactions.find(t => t.id === transactionId.value)
)

const category = computed(() => 
  transaction.value ? transactionStore.getCategoryById(transaction.value.categoryId) : null
)

const formatCurrency = (amount: number) => settingsStore.formatCurrency(amount)

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('zh-CN')
}

const formatDateTime = (dateString: string) => {
  return new Date(dateString).toLocaleString('zh-CN')
}

const goBack = () => {
  router.back()
}

const deleteTransaction = () => {
  if (transaction.value) {
    transactionStore.deleteTransaction(transaction.value.id)
    showDeleteConfirm.value = false
    router.push('/')
  }
}

onMounted(() => {
  // 如果交易不存在，返回首页
  if (!transaction.value) {
    router.push('/')
  }
})
</script>
