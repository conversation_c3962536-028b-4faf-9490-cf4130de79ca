// 基础服务
export { BaseService } from './base/BaseService'

// 存储服务
export { StorageService, storageService } from './storage/StorageService'

// 验证服务
export { ValidationService, validationService } from './validation/ValidationService'

// 数据服务
export { TransactionService, transactionService } from './data/TransactionService'
export { CategoryService, categoryService } from './data/CategoryService'

// 错误处理服务
export { ErrorService, errorService } from './ErrorService'

// 性能监控服务
export { PerformanceService, performanceService } from './PerformanceService'

// 缓存服务
export { CacheService, cacheService } from './CacheService'

// 服务类型
export type {
  ServiceResponse,
  ServiceError,
  StorageResult,
  ValidationResult,
  ValidationError
} from '@/types'
