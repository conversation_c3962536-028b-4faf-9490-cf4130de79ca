import { BaseService } from '../base/BaseService'
import { storageService } from '../storage/StorageService'
import { validationService } from '../validation/ValidationService'
import type { 
  Category, 
  TransactionType,
  ServiceResponse
} from '@/types'

/**
 * 分类数据服务
 * 负责分类数据的CRUD操作和业务逻辑
 */
export class CategoryService extends BaseService {
  private static instance: CategoryService
  private readonly STORAGE_KEY = 'categories'
  private categoriesCache: Category[] | null = null
  private lastCacheUpdate: number = 0
  private readonly CACHE_TTL = 10 * 60 * 1000 // 10分钟缓存

  private constructor() {
    super()
  }

  /**
   * 获取单例实例
   */
  public static getInstance(): CategoryService {
    if (!CategoryService.instance) {
      CategoryService.instance = new CategoryService()
    }
    return CategoryService.instance
  }

  /**
   * 获取所有分类
   */
  public async getAllCategories(forceRefresh: boolean = false): Promise<ServiceResponse<Category[]>> {
    return this.safeExecute(async () => {
      // 检查缓存
      if (!forceRefresh && this.isCacheValid()) {
        return this.categoriesCache!
      }

      const result = await storageService.getItem<Category[]>(this.STORAGE_KEY)
      if (!result.success) {
        throw new Error(result.error?.message || 'Failed to load categories')
      }

      let categories = result.data || []

      // 如果没有分类，初始化默认分类
      if (categories.length === 0) {
        categories = this.getDefaultCategories()
        const saveResult = await storageService.setItem(this.STORAGE_KEY, categories)
        if (!saveResult.success) {
          this.log('warn', 'Failed to save default categories', saveResult.error)
        }
      }

      // 按排序权重和名称排序
      categories.sort((a, b) => {
        if (a.order !== b.order) {
          return a.order - b.order
        }
        return a.name.localeCompare(b.name)
      })

      // 更新缓存
      this.categoriesCache = categories
      this.lastCacheUpdate = Date.now()

      return categories
    }, 'GET_CATEGORIES_ERROR')
  }

  /**
   * 根据类型获取分类
   */
  public async getCategoriesByType(type: TransactionType): Promise<ServiceResponse<Category[]>> {
    return this.safeExecute(async () => {
      const categoriesResult = await this.getAllCategories()
      if (!categoriesResult.success) {
        throw new Error('Failed to load categories')
      }

      return categoriesResult.data!.filter(category => category.type === type)
    }, 'GET_CATEGORIES_BY_TYPE_ERROR')
  }

  /**
   * 根据ID获取分类
   */
  public async getCategoryById(id: string): Promise<ServiceResponse<Category | null>> {
    return this.safeExecute(async () => {
      const categoriesResult = await this.getAllCategories()
      if (!categoriesResult.success) {
        throw new Error('Failed to load categories')
      }

      const category = categoriesResult.data!.find(c => c.id === id)
      return category || null
    }, 'GET_CATEGORY_BY_ID_ERROR')
  }

  /**
   * 创建分类
   */
  public async createCategory(
    categoryData: Omit<Category, 'id'>
  ): Promise<ServiceResponse<Category>> {
    return this.safeExecute(async () => {
      // 验证数据
      const validationResult = validationService.validateCategory(categoryData)
      if (!validationResult.success || !validationResult.data!.valid) {
        const errors = validationResult.data?.errors || []
        throw new Error(`Validation failed: ${errors.map(e => e.message).join(', ')}`)
      }

      // 检查名称是否重复
      const categoriesResult = await this.getAllCategories()
      if (!categoriesResult.success) {
        throw new Error('Failed to load existing categories')
      }

      const existingCategories = categoriesResult.data!
      const nameExists = existingCategories.some(
        c => c.name.toLowerCase() === categoryData.name.toLowerCase() && c.type === categoryData.type
      )

      if (nameExists) {
        throw new Error(`Category name "${categoryData.name}" already exists for ${categoryData.type}`)
      }

      // 创建新分类
      const category: Category = {
        ...categoryData,
        id: this.generateId(),
        order: categoryData.order ?? this.getNextOrder(existingCategories, categoryData.type)
      }

      // 添加新分类并保存
      const categories = [...existingCategories, category]
      const saveResult = await storageService.setItem(this.STORAGE_KEY, categories)
      
      if (!saveResult.success) {
        throw new Error(saveResult.error?.message || 'Failed to save category')
      }

      // 更新缓存
      this.categoriesCache = categories
      this.lastCacheUpdate = Date.now()

      this.log('info', 'Category created successfully', { id: category.id, name: category.name })
      return category
    }, 'CREATE_CATEGORY_ERROR')
  }

  /**
   * 更新分类
   */
  public async updateCategory(
    id: string,
    updates: Partial<Omit<Category, 'id'>>
  ): Promise<ServiceResponse<Category>> {
    return this.safeExecute(async () => {
      // 验证更新数据
      if (Object.keys(updates).length > 0) {
        const validationResult = validationService.validateCategory(updates)
        if (!validationResult.success || !validationResult.data!.valid) {
          const errors = validationResult.data?.errors || []
          throw new Error(`Validation failed: ${errors.map(e => e.message).join(', ')}`)
        }
      }

      // 获取现有分类
      const categoriesResult = await this.getAllCategories()
      if (!categoriesResult.success) {
        throw new Error('Failed to load categories')
      }

      const categories = categoriesResult.data!
      const index = categories.findIndex(c => c.id === id)
      
      if (index === -1) {
        throw new Error(`Category with id ${id} not found`)
      }

      const existingCategory = categories[index]

      // 检查名称是否重复（如果更新了名称）
      if (updates.name && updates.name !== existingCategory.name) {
        const nameExists = categories.some(
          c => c.id !== id && 
               c.name.toLowerCase() === updates.name!.toLowerCase() && 
               c.type === (updates.type || existingCategory.type)
        )

        if (nameExists) {
          throw new Error(`Category name "${updates.name}" already exists`)
        }
      }

      // 更新分类
      const updatedCategory: Category = {
        ...existingCategory,
        ...updates
      }

      categories[index] = updatedCategory

      // 保存更新
      const saveResult = await storageService.setItem(this.STORAGE_KEY, categories)
      if (!saveResult.success) {
        throw new Error(saveResult.error?.message || 'Failed to save updated category')
      }

      // 更新缓存
      this.categoriesCache = categories
      this.lastCacheUpdate = Date.now()

      this.log('info', 'Category updated successfully', { id, name: updatedCategory.name })
      return updatedCategory
    }, 'UPDATE_CATEGORY_ERROR')
  }

  /**
   * 删除分类
   */
  public async deleteCategory(id: string): Promise<ServiceResponse<boolean>> {
    return this.safeExecute(async () => {
      // 获取现有分类
      const categoriesResult = await this.getAllCategories()
      if (!categoriesResult.success) {
        throw new Error('Failed to load categories')
      }

      const categories = categoriesResult.data!
      const index = categories.findIndex(c => c.id === id)
      
      if (index === -1) {
        throw new Error(`Category with id ${id} not found`)
      }

      const category = categories[index]

      // 检查是否为默认分类
      if (category.isDefault) {
        throw new Error('Cannot delete default category')
      }

      // 删除分类
      categories.splice(index, 1)

      // 保存更新
      const saveResult = await storageService.setItem(this.STORAGE_KEY, categories)
      if (!saveResult.success) {
        throw new Error(saveResult.error?.message || 'Failed to save after deletion')
      }

      // 更新缓存
      this.categoriesCache = categories
      this.lastCacheUpdate = Date.now()

      this.log('info', 'Category deleted successfully', { id, name: category.name })
      return true
    }, 'DELETE_CATEGORY_ERROR')
  }

  /**
   * 获取默认分类
   */
  private getDefaultCategories(): Category[] {
    return [
      // 支出分类
      { id: 'exp_food', name: '餐饮', icon: 'utensils', color: '#ef4444', type: 'expense', isDefault: true, order: 1 },
      { id: 'exp_transport', name: '交通', icon: 'truck', color: '#3b82f6', type: 'expense', isDefault: true, order: 2 },
      { id: 'exp_shopping', name: '购物', icon: 'shopping-bag', color: '#8b5cf6', type: 'expense', isDefault: true, order: 3 },
      { id: 'exp_entertainment', name: '娱乐', icon: 'film', color: '#f59e0b', type: 'expense', isDefault: true, order: 4 },
      { id: 'exp_healthcare', name: '医疗', icon: 'heart', color: '#10b981', type: 'expense', isDefault: true, order: 5 },
      { id: 'exp_education', name: '教育', icon: 'academic-cap', color: '#6366f1', type: 'expense', isDefault: true, order: 6 },
      { id: 'exp_housing', name: '住房', icon: 'home', color: '#84cc16', type: 'expense', isDefault: true, order: 7 },
      { id: 'exp_other', name: '其他', icon: 'ellipsis-horizontal', color: '#6b7280', type: 'expense', isDefault: true, order: 8 },
      
      // 收入分类
      { id: 'inc_salary', name: '工资', icon: 'banknotes', color: '#22c55e', type: 'income', isDefault: true, order: 1 },
      { id: 'inc_bonus', name: '奖金', icon: 'trophy', color: '#f59e0b', type: 'income', isDefault: true, order: 2 },
      { id: 'inc_investment', name: '投资', icon: 'chart-bar', color: '#3b82f6', type: 'income', isDefault: true, order: 3 },
      { id: 'inc_freelance', name: '兼职', icon: 'computer-desktop', color: '#8b5cf6', type: 'income', isDefault: true, order: 4 },
      { id: 'inc_other', name: '其他', icon: 'currency-dollar', color: '#6b7280', type: 'income', isDefault: true, order: 5 }
    ]
  }

  /**
   * 获取下一个排序权重
   */
  private getNextOrder(categories: Category[], type: TransactionType): number {
    const typeCategories = categories.filter(c => c.type === type)
    if (typeCategories.length === 0) {
      return 1
    }
    return Math.max(...typeCategories.map(c => c.order)) + 1
  }

  /**
   * 生成唯一ID
   */
  private generateId(): string {
    return `cat_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * 检查缓存是否有效
   */
  private isCacheValid(): boolean {
    return this.categoriesCache !== null && 
           (Date.now() - this.lastCacheUpdate) < this.CACHE_TTL
  }

  /**
   * 清除缓存
   */
  public clearCache(): void {
    this.categoriesCache = null
    this.lastCacheUpdate = 0
  }
}

// 导出单例实例
export const categoryService = CategoryService.getInstance()
