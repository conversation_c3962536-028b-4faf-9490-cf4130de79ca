import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'

// ==========================================================================
// 路由配置 - Route Configuration
// ==========================================================================

const routes: RouteRecordRaw[] = [
  {
    path: '/',
    name: 'Home',
    component: () => import('@/views/HomeView.vue'),
    meta: {
      title: '首页',
      icon: 'HomeIcon',
      showInNavigation: true,
      requiresAuth: false
    }
  },

  {
    path: '/stats',
    name: 'Stats',
    component: () => import('@/views/StatsView.vue'),
    meta: {
      title: '统计',
      icon: 'ChartBarIcon',
      showInNavigation: true,
      requiresAuth: false
    }
  },
  {
    path: '/settings',
    name: 'Settings',
    component: () => import('@/views/SettingsView.vue'),
    meta: {
      title: '设置',
      icon: 'CogIcon',
      showInNavigation: true,
      requiresAuth: false
    }
  },
  {
    path: '/transaction/:id',
    name: 'TransactionDetail',
    component: () => import('@/views/TransactionDetailView.vue'),
    meta: {
      title: '交易详情',
      showInNavigation: false,
      requiresAuth: false
    }
  },
  {
    path: '/category/:id',
    name: 'CategoryDetail',
    component: () => import('@/views/CategoryDetailView.vue'),
    meta: {
      title: '分类详情',
      showInNavigation: false,
      requiresAuth: false
    }
  },
  {
    path: '/backup',
    name: 'Backup',
    component: () => import('@/views/BackupView.vue'),
    meta: {
      title: '数据备份',
      showInNavigation: false,
      requiresAuth: false
    }
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('@/views/NotFoundView.vue'),
    meta: {
      title: '页面未找到',
      showInNavigation: false,
      requiresAuth: false
    }
  }
]

// ==========================================================================
// 路由实例 - Router Instance
// ==========================================================================

const router = createRouter({
  history: createWebHistory(),
  routes,
  scrollBehavior(to, from, savedPosition) {
    // 如果有保存的滚动位置，恢复它
    if (savedPosition) {
      return savedPosition
    }
    // 否则滚动到顶部
    return { top: 0 }
  }
})

// ==========================================================================
// 路由守卫 - Route Guards
// ==========================================================================

// 全局前置守卫
router.beforeEach((to, from, next) => {
  // 设置页面标题
  if (to.meta?.title) {
    document.title = `${to.meta.title} - Money Tracker`
  } else {
    document.title = 'Money Tracker'
  }
  
  // 这里可以添加认证检查
  // if (to.meta?.requiresAuth && !isAuthenticated()) {
  //   next('/login')
  //   return
  // }
  
  next()
})

// 全局后置钩子
router.afterEach((to, from) => {
  // 这里可以添加页面访问统计等逻辑
  console.log(`Navigation: ${from.name} -> ${to.name}`)
})

// ==========================================================================
// 类型扩展 - Type Extensions
// ==========================================================================

declare module 'vue-router' {
  interface RouteMeta {
    title?: string
    icon?: string
    showInNavigation?: boolean
    requiresAuth?: boolean
  }
}

export default router
