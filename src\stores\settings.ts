import { defineStore } from 'pinia'
import { ref, computed, watch } from 'vue'
import { storageService } from '@/services'
import type { AppSettings, ThemeMode, LanguageCode, AsyncState } from '@/types'

// ==========================================================================
// 常量定义 - Constants
// ==========================================================================

const STORAGE_KEY = 'app-settings'

const DEFAULT_SETTINGS: AppSettings = {
  theme: 'system',
  language: 'zh-CN',
  notifications: true,
  biometric: false,
  backup: {
    enabled: false,
    frequency: 7
  }
}

/**
 * 应用设置状态管理
 * 使用服务层进行数据持久化
 */
export const useSettingsStore = defineStore('settings', () => {
  // ==========================================================================
  // 状态定义 - State
  // ==========================================================================

  /** 设置数据状态 */
  const settingsState = ref<AsyncState<AppSettings>>({
    data: { ...DEFAULT_SETTINGS },
    loading: false,
    error: null,
    lastUpdated: null
  })

  // ==========================================================================
  // 计算属性 - Computed
  // ==========================================================================

  /** 当前设置 */
  const settings = computed(() => settingsState.value.data || DEFAULT_SETTINGS)

  /** 当前主题是否为深色模式 */
  const isDarkMode = computed(() => {
    if (settings.value.theme === 'system') {
      return window.matchMedia('(prefers-color-scheme: dark)').matches
    }
    return settings.value.theme === 'dark'
  })

  /** 当前实际主题 */
  const currentTheme = computed(() => {
    if (settings.value.theme === 'system') {
      return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light'
    }
    return settings.value.theme
  })

  /** 货币格式化器 - 固定使用人民币 */
  const currencyFormatter = computed(() => {
    return new Intl.NumberFormat('zh-CN', {
      style: 'currency',
      currency: 'CNY',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    })
  })

  /** 是否正在加载 */
  const isLoading = computed(() => settingsState.value.loading)

  /** 是否有错误 */
  const hasError = computed(() => !!settingsState.value.error)

  /** 错误信息 */
  const errorMessage = computed(() => settingsState.value.error)

  // ==========================================================================
  // 设置操作方法 - Settings Actions
  // ==========================================================================

  /** 加载设置 */
  async function loadSettings() {
    if (settingsState.value.loading) return

    settingsState.value.loading = true
    settingsState.value.error = null

    try {
      const result = await storageService.getItem<AppSettings>(STORAGE_KEY)

      if (result.success && result.data) {
        // 合并默认设置和保存的设置
        settingsState.value.data = { ...DEFAULT_SETTINGS, ...result.data }
        settingsState.value.lastUpdated = Date.now()
      } else {
        // 使用默认设置
        settingsState.value.data = { ...DEFAULT_SETTINGS }
      }

      // 应用主题
      applyTheme()
    } catch (error) {
      settingsState.value.error = error instanceof Error ? error.message : 'Unknown error'
      console.error('Failed to load settings:', error)
    } finally {
      settingsState.value.loading = false
    }
  }

  /** 保存设置 */
  async function saveSettings() {
    try {
      const result = await storageService.setItem(STORAGE_KEY, settingsState.value.data)
      if (!result.success) {
        throw new Error(result.error?.message || 'Failed to save settings')
      }
      settingsState.value.lastUpdated = Date.now()
    } catch (error) {
      settingsState.value.error = error instanceof Error ? error.message : 'Unknown error'
      console.error('Failed to save settings:', error)
    }
  }

  /** 更新设置 */
  async function updateSettings(updates: Partial<AppSettings>) {
    if (!settingsState.value.data) return

    const oldTheme = settingsState.value.data.theme
    settingsState.value.data = { ...settingsState.value.data, ...updates }

    // 如果主题发生变化，应用新主题
    if (updates.theme && updates.theme !== oldTheme) {
      applyTheme()
    }

    await saveSettings()
  }

  /** 更新主题 */
  async function updateTheme(theme: ThemeMode) {
    await updateSettings({ theme })
  }



  /** 更新语言 */
  async function updateLanguage(language: LanguageCode) {
    await updateSettings({ language })
  }

  /** 切换通知设置 */
  async function toggleNotifications() {
    await updateSettings({ notifications: !settings.value.notifications })
  }

  /** 切换生物识别设置 */
  async function toggleBiometric() {
    await updateSettings({ biometric: !settings.value.biometric })
  }

  /** 更新备份设置 */
  async function updateBackupSettings(backupSettings: Partial<AppSettings['backup']>) {
    await updateSettings({
      backup: { ...settings.value.backup, ...backupSettings }
    })
  }

  // ==========================================================================
  // 工具方法 - Utility Methods
  // ==========================================================================

  /** 格式化货币 */
  function formatCurrency(amount: number): string {
    // 将分转换为元
    const yuan = Math.round(amount) / 100
    return currencyFormatter.value.format(yuan)
  }

  /** 应用主题到DOM */
  function applyTheme() {
    const html = document.documentElement
    const theme = settings.value.theme

    if (theme === 'dark') {
      html.classList.add('dark')
    } else if (theme === 'light') {
      html.classList.remove('dark')
    } else {
      // system theme
      const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches
      html.classList.toggle('dark', prefersDark)
    }
  }

  /** 重置设置到默认值 */
  async function resetSettings() {
    settingsState.value.data = { ...DEFAULT_SETTINGS }
    applyTheme()
    await saveSettings()
  }

  /** 导出设置 */
  function exportSettings(): string {
    return JSON.stringify(settings.value, null, 2)
  }

  /** 导入设置 */
  async function importSettings(settingsJson: string): Promise<boolean> {
    try {
      const importedSettings = JSON.parse(settingsJson)

      // 验证导入的设置格式
      if (typeof importedSettings === 'object' && importedSettings !== null) {
        const mergedSettings = {
          ...DEFAULT_SETTINGS,
          ...importedSettings,
          backup: {
            ...DEFAULT_SETTINGS.backup,
            ...importedSettings.backup
          }
        }

        settingsState.value.data = mergedSettings
        applyTheme()
        await saveSettings()
        return true
      }
      return false
    } catch (error) {
      console.error('Failed to import settings:', error)
      settingsState.value.error = error instanceof Error ? error.message : 'Import failed'
      return false
    }
  }

  /** 初始化设置 */
  async function initialize() {
    await loadSettings()
  }

  /** 清除错误状态 */
  function clearError() {
    settingsState.value.error = null
  }

  // ==========================================================================
  // 系统主题监听 - System Theme Listener
  // ==========================================================================

  // 监听系统主题变化
  if (typeof window !== 'undefined') {
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
    mediaQuery.addEventListener('change', () => {
      if (settings.value.theme === 'system') {
        applyTheme()
      }
    })
  }

  // ==========================================================================
  // 返回接口 - Return Interface
  // ==========================================================================

  return {
    // 响应式状态
    settings,
    isLoading,
    hasError,
    errorMessage,

    // 计算属性
    isDarkMode,
    currentTheme,
    currencyFormatter,

    // 数据操作方法
    initialize,
    loadSettings,
    saveSettings,
    updateSettings,

    // 设置更新方法
    updateTheme,
    updateLanguage,
    toggleNotifications,
    toggleBiometric,
    updateBackupSettings,

    // 工具方法
    formatCurrency,
    resetSettings,
    exportSettings,
    importSettings,
    clearError
  }
})
