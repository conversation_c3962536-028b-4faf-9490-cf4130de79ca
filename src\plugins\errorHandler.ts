/**
 * 全局错误处理插件
 * Global error handling plugin
 */

import type { App } from 'vue'
import { h } from 'vue'
import { errorService } from '@/services/ErrorService'
import { globalErrorHandler } from '@/composables/useErrorHandler'

/**
 * 错误处理插件选项
 */
export interface ErrorHandlerOptions {
  /** 是否启用开发模式 */
  development?: boolean
  /** 是否显示错误通知 */
  showNotifications?: boolean
  /** 是否上报错误 */
  reportErrors?: boolean
  /** 错误监控服务配置 */
  monitoring?: {
    dsn?: string
    environment?: string
    release?: string
  }
  /** 自定义错误处理器 */
  customHandlers?: {
    [key: string]: (error: any) => void
  }
}

/**
 * 错误处理插件
 */
export default {
  install(app: App, options: ErrorHandlerOptions = {}) {
    const {
      development = process.env.NODE_ENV === 'development',
      showNotifications = true,
      reportErrors = !development,
      monitoring,
      customHandlers = {}
    } = options

    // 设置Vue错误处理器
    app.config.errorHandler = (err, vm, info) => {
      const error = errorService.createError(
        'VUE_ERROR',
        err.message,
        'error',
        {
          componentInfo: info,
          componentName: vm?.$options?.name || 'Unknown',
          stack: err.stack
        }
      )

      errorService.handleError(error)

      // 在开发模式下也输出到控制台
      if (development) {
        console.error('Vue Error:', err, vm, info)
      }
    }

    // 设置Vue警告处理器
    app.config.warnHandler = (msg, vm, trace) => {
      const error = errorService.createError(
        'VUE_WARNING',
        msg,
        'warning',
        {
          componentTrace: trace,
          componentName: vm?.$options?.name || 'Unknown'
        }
      )

      errorService.handleError(error)

      // 在开发模式下也输出到控制台
      if (development) {
        console.warn('Vue Warning:', msg, vm, trace)
      }
    }

    // 注册自定义错误处理器
    Object.entries(customHandlers).forEach(([type, handler]) => {
      errorService.registerHandler(type, handler)
    })

    // 注册通知处理器
    if (showNotifications) {
      errorService.registerHandler('*', (error) => {
        // 只显示用户级别的错误
        if (error.level === 'error' || error.level === 'critical') {
          showErrorNotification(error)
        }
      })
    }

    // 初始化错误监控
    if (monitoring && reportErrors) {
      initializeMonitoring(monitoring)
    }

    // 提供全局错误处理方法
    app.provide('$errorHandler', globalErrorHandler)
    app.config.globalProperties.$errorHandler = globalErrorHandler

    // 提供错误服务
    app.provide('$errorService', errorService)
    app.config.globalProperties.$errorService = errorService

    // 添加全局方法
    app.config.globalProperties.$handleError = globalErrorHandler.handleError
    app.config.globalProperties.$wrapAsync = globalErrorHandler.wrapAsync
    app.config.globalProperties.$retry = globalErrorHandler.retry
  }
}

/**
 * 显示错误通知
 */
function showErrorNotification(error: any): void {
  // 创建自定义事件来触发通知显示
  const event = new CustomEvent('app:show-notification', {
    detail: {
      type: error.level === 'critical' ? 'error' : 'warning',
      title: getErrorTitle(error),
      message: getUserFriendlyMessage(error),
      duration: error.level === 'critical' ? 0 : 5000, // 严重错误不自动关闭
      actions: error.level === 'critical' ? [
        {
          label: '刷新页面',
          type: 'primary',
          handler: () => window.location.reload()
        },
        {
          label: '查看详情',
          type: 'default',
          handler: () => showErrorDetails(error)
        }
      ] : undefined
    }
  })

  window.dispatchEvent(event)
}

/**
 * 获取错误标题
 */
function getErrorTitle(error: any): string {
  const titleMap: Record<string, string> = {
    'VUE_ERROR': 'Vue组件错误',
    'NETWORK_ERROR': '网络错误',
    'VALIDATION_ERROR': '数据验证错误',
    'STORAGE_ERROR': '存储错误',
    'PERMISSION_DENIED': '权限错误',
    'NOT_FOUND': '资源不存在',
    'SERVER_ERROR': '服务器错误',
    'TIMEOUT': '请求超时'
  }

  return titleMap[error.code] || '系统错误'
}

/**
 * 获取用户友好的错误信息
 */
function getUserFriendlyMessage(error: any): string {
  const messageMap: Record<string, string> = {
    'VUE_ERROR': '页面组件出现异常，请刷新页面重试',
    'NETWORK_ERROR': '网络连接失败，请检查网络设置',
    'VALIDATION_ERROR': '输入的数据格式不正确',
    'STORAGE_ERROR': '数据存储失败，请检查存储空间',
    'PERMISSION_DENIED': '您没有执行此操作的权限',
    'NOT_FOUND': '请求的资源不存在',
    'SERVER_ERROR': '服务器内部错误，请稍后重试',
    'TIMEOUT': '请求超时，请稍后重试'
  }

  return messageMap[error.code] || error.message || '发生了未知错误'
}

/**
 * 显示错误详情
 */
function showErrorDetails(error: any): void {
  const event = new CustomEvent('app:show-error-details', {
    detail: { error }
  })
  window.dispatchEvent(event)
}

/**
 * 初始化错误监控
 */
function initializeMonitoring(config: any): void {
  // 这里可以集成第三方错误监控服务
  // 例如 Sentry、LogRocket、Bugsnag 等
  
  if (config.dsn) {
    // 示例：Sentry 集成
    // import * as Sentry from '@sentry/vue'
    // Sentry.init({
    //   dsn: config.dsn,
    //   environment: config.environment,
    //   release: config.release
    // })
    
    console.log('Error monitoring initialized with DSN:', config.dsn)
  }
}

/**
 * 错误边界组件
 */
export const ErrorBoundary = {
  name: 'ErrorBoundary',
  props: {
    fallback: {
      type: [String, Object, Function],
      default: null
    },
    onError: {
      type: Function,
      default: null
    }
  },
  data() {
    return {
      hasError: false,
      error: null
    }
  },
  errorCaptured(err: Error, vm: any, info: string) {
    this.hasError = true
    this.error = err

    // 处理错误
    const serviceError = errorService.createError(
      'COMPONENT_BOUNDARY_ERROR',
      err.message,
      'error',
      {
        componentInfo: info,
        componentName: vm?.$options?.name || 'Unknown',
        stack: err.stack
      }
    )

    errorService.handleError(serviceError)

    // 调用自定义错误处理器
    if (this.onError) {
      this.onError(err, vm, info)
    }

    // 阻止错误继续传播
    return false
  },
  render() {
    if (this.hasError) {
      // 渲染错误回退UI
      if (this.fallback) {
        if (typeof this.fallback === 'string') {
          return h('div', { class: 'error-boundary' }, this.fallback)
        } else if (typeof this.fallback === 'function') {
          return this.fallback(this.error)
        } else {
          return h(this.fallback, { error: this.error })
        }
      }

      // 默认错误UI
      return h('div', {
        class: 'error-boundary bg-red-50 border border-red-200 rounded-lg p-4 text-red-700'
      }, [
        h('h3', { class: 'font-medium mb-2' }, '组件加载失败'),
        h('p', { class: 'text-sm' }, '该组件遇到了错误，请刷新页面重试。'),
        h('button', {
          class: 'mt-2 px-3 py-1 bg-red-600 text-white text-sm rounded hover:bg-red-700',
          onClick: () => window.location.reload()
        }, '刷新页面')
      ])
    }

    // 正常渲染子组件
    return this.$slots.default?.()
  }
}

/**
 * 错误处理指令
 */
export const vErrorHandler = {
  mounted(el: HTMLElement, binding: any) {
    const handler = binding.value || (() => {})
    
    el.addEventListener('error', (event) => {
      const error = errorService.createError(
        'DOM_ERROR',
        'DOM element error',
        'warning',
        {
          element: el.tagName,
          src: (event.target as any)?.src,
          href: (event.target as any)?.href
        }
      )

      errorService.handleError(error)
      handler(event)
    })
  }
}
