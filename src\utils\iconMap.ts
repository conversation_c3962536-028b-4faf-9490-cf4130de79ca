import {
  // 支出分类图标
  CakeIcon,
  TruckIcon,
  ShoppingBagIcon,
  FilmIcon,
  HeartIcon,
  AcademicCapIcon,
  HomeIcon,
  EllipsisHorizontalIcon,
  // 收入分类图标
  BanknotesIcon,
  TrophyIcon,
  ChartBarIcon,
  ComputerDesktopIcon,
  CurrencyDollarIcon,
  // 默认图标
  DocumentTextIcon
} from '@heroicons/vue/24/outline'

import type { Component } from 'vue'

// 图标映射表
export const iconMap: Record<string, Component> = {
  // 支出分类
  'utensils': CakeIcon, // 使用蛋糕图标代替餐具
  'truck': TruckIcon,
  'shopping-bag': ShoppingBagIcon,
  'film': FilmIcon,
  'heart': HeartIcon,
  'academic-cap': AcademicCapIcon,
  'home': HomeIcon,
  'ellipsis-horizontal': EllipsisHorizontalIcon,
  
  // 收入分类
  'banknotes': BanknotesIcon,
  'trophy': TrophyIcon,
  'chart-bar': ChartBarIcon,
  'computer-desktop': ComputerDesktopIcon,
  'currency-dollar': CurrencyDollarIcon,
  
  // 默认图标
  'default': DocumentTextIcon
}

/**
 * 根据图标名称获取对应的Vue组件
 * @param iconName 图标名称
 * @returns Vue组件
 */
export function getIconComponent(iconName: string): Component {
  return iconMap[iconName] || iconMap.default
}

/**
 * 检查图标是否存在
 * @param iconName 图标名称
 * @returns 是否存在
 */
export function hasIcon(iconName: string): boolean {
  return iconName in iconMap
}
