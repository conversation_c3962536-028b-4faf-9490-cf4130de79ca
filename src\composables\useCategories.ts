import { computed, ref } from 'vue'
import { useTransactionStore } from '@/stores/transactions'
import type { Category, TransactionType } from '@/types'

/**
 * 分类数据管理组合式函数
 * 提供分类相关的响应式数据和操作方法
 */
export function useCategories() {
  const store = useTransactionStore()

  // ==========================================================================
  // 响应式状态 - Reactive State
  // ==========================================================================

  /** 是否显示加载状态 */
  const showLoading = ref(false)

  // ==========================================================================
  // 计算属性 - Computed Properties
  // ==========================================================================

  /** 所有分类 */
  const categories = computed(() => store.categories)

  /** 支出分类 */
  const expenseCategories = computed(() => store.expenseCategories)

  /** 收入分类 */
  const incomeCategories = computed(() => store.incomeCategories)

  /** 默认分类 */
  const defaultCategories = computed(() => 
    categories.value.filter(cat => cat.isDefault)
  )

  /** 自定义分类 */
  const customCategories = computed(() => 
    categories.value.filter(cat => !cat.isDefault)
  )

  /** 是否有分类数据 */
  const hasCategories = computed(() => categories.value.length > 0)

  /** 是否正在加载 */
  const isLoading = computed(() => store.isLoading || showLoading.value)

  /** 是否有错误 */
  const hasError = computed(() => store.hasError)

  /** 错误信息 */
  const errorMessage = computed(() => store.errorMessage)

  // ==========================================================================
  // 分类操作方法 - Category Actions
  // ==========================================================================

  /** 初始化分类数据 */
  async function initialize() {
    showLoading.value = true
    try {
      await store.loadCategories()
    } finally {
      showLoading.value = false
    }
  }

  /** 刷新分类数据 */
  async function refresh() {
    showLoading.value = true
    try {
      await store.loadCategories(true)
    } finally {
      showLoading.value = false
    }
  }

  /** 创建分类 */
  async function createCategory(categoryData: Omit<Category, 'id'>) {
    showLoading.value = true
    try {
      const result = await store.createCategory(categoryData)
      if (result) {
        return { success: true, data: result }
      } else {
        return { success: false, error: store.errorMessage }
      }
    } finally {
      showLoading.value = false
    }
  }

  /** 更新分类 */
  async function updateCategory(
    id: string,
    updates: Partial<Omit<Category, 'id'>>
  ) {
    showLoading.value = true
    try {
      const success = await store.updateCategory(id, updates)
      if (success) {
        return { success: true }
      } else {
        return { success: false, error: store.errorMessage }
      }
    } finally {
      showLoading.value = false
    }
  }

  /** 删除分类 */
  async function deleteCategory(id: string) {
    showLoading.value = true
    try {
      // 检查是否有交易使用此分类
      const hasTransactions = store.transactions.some(t => t.categoryId === id)
      if (hasTransactions) {
        return { 
          success: false, 
          error: '无法删除有交易记录的分类，请先删除相关交易记录' 
        }
      }

      const success = await store.deleteCategory(id)
      if (success) {
        return { success: true }
      } else {
        return { success: false, error: store.errorMessage }
      }
    } finally {
      showLoading.value = false
    }
  }

  // ==========================================================================
  // 查询方法 - Query Methods
  // ==========================================================================

  /** 根据ID获取分类 */
  function getCategoryById(id: string): Category | undefined {
    return store.getCategoryById(id)
  }

  /** 根据类型获取分类 */
  function getCategoriesByType(type: TransactionType): Category[] {
    return categories.value.filter(cat => cat.type === type)
  }

  /** 根据名称搜索分类 */
  function searchCategories(keyword: string): Category[] {
    if (!keyword.trim()) return categories.value
    
    const searchTerm = keyword.toLowerCase().trim()
    return categories.value.filter(cat => 
      cat.name.toLowerCase().includes(searchTerm)
    )
  }

  /** 检查分类名称是否已存在 */
  function isCategoryNameExists(name: string, type: TransactionType, excludeId?: string): boolean {
    return categories.value.some(cat => 
      cat.name.toLowerCase() === name.toLowerCase() && 
      cat.type === type &&
      cat.id !== excludeId
    )
  }

  /** 获取分类的使用统计 */
  function getCategoryUsageStats(categoryId: string) {
    const transactions = store.getTransactionsByCategory(categoryId)
    const totalAmount = transactions.reduce((sum, t) => sum + t.amount, 0)
    
    return {
      transactionCount: transactions.length,
      totalAmount,
      lastUsed: transactions.length > 0 
        ? Math.max(...transactions.map(t => new Date(t.date).getTime()))
        : null
    }
  }

  /** 获取所有分类的使用统计 */
  function getAllCategoryStats() {
    return categories.value.map(category => ({
      ...category,
      usage: getCategoryUsageStats(category.id)
    }))
  }

  // ==========================================================================
  // 分类排序方法 - Category Sorting Methods
  // ==========================================================================

  /** 更新分类排序 */
  async function updateCategoryOrder(categoryId: string, newOrder: number) {
    return await updateCategory(categoryId, { order: newOrder })
  }

  /** 批量更新分类排序 */
  async function updateCategoriesOrder(updates: Array<{ id: string; order: number }>) {
    const results = await Promise.all(
      updates.map(update => updateCategoryOrder(update.id, update.order))
    )
    
    const successCount = results.filter(r => r.success).length
    return {
      success: successCount === updates.length,
      successCount,
      totalCount: updates.length
    }
  }

  // ==========================================================================
  // 分类验证方法 - Category Validation Methods
  // ==========================================================================

  /** 验证分类数据 */
  function validateCategory(categoryData: Partial<Category>): {
    valid: boolean
    errors: string[]
  } {
    const errors: string[] = []

    // 验证名称
    if (!categoryData.name || !categoryData.name.trim()) {
      errors.push('分类名称不能为空')
    } else if (categoryData.name.trim().length > 20) {
      errors.push('分类名称不能超过20个字符')
    }

    // 验证类型
    if (!categoryData.type || !['income', 'expense'].includes(categoryData.type)) {
      errors.push('分类类型必须是收入或支出')
    }

    // 验证图标
    if (!categoryData.icon || !categoryData.icon.trim()) {
      errors.push('分类图标不能为空')
    }

    // 验证颜色
    if (!categoryData.color || !categoryData.color.trim()) {
      errors.push('分类颜色不能为空')
    } else if (!/^#[0-9A-Fa-f]{6}$/.test(categoryData.color)) {
      errors.push('分类颜色格式不正确')
    }

    // 验证排序权重
    if (categoryData.order !== undefined && 
        (!Number.isInteger(categoryData.order) || categoryData.order < 0)) {
      errors.push('排序权重必须是非负整数')
    }

    // 验证名称唯一性
    if (categoryData.name && categoryData.type) {
      const exists = isCategoryNameExists(
        categoryData.name.trim(), 
        categoryData.type,
        (categoryData as Category).id
      )
      if (exists) {
        errors.push('该分类名称已存在')
      }
    }

    return {
      valid: errors.length === 0,
      errors
    }
  }

  // ==========================================================================
  // 工具方法 - Utility Methods
  // ==========================================================================

  /** 清除错误状态 */
  function clearError() {
    store.clearErrors()
  }

  /** 获取下一个排序权重 */
  function getNextOrder(type: TransactionType): number {
    const typeCategories = getCategoriesByType(type)
    if (typeCategories.length === 0) return 1
    return Math.max(...typeCategories.map(cat => cat.order)) + 1
  }

  /** 获取分类颜色选项 */
  function getColorOptions(): string[] {
    return [
      '#ef4444', '#f97316', '#f59e0b', '#eab308',
      '#84cc16', '#22c55e', '#10b981', '#14b8a6',
      '#06b6d4', '#0ea5e9', '#3b82f6', '#6366f1',
      '#8b5cf6', '#a855f7', '#d946ef', '#ec4899',
      '#f43f5e', '#6b7280', '#374151', '#1f2937'
    ]
  }

  /** 获取图标选项 */
  function getIconOptions(): string[] {
    return [
      'banknotes', 'credit-card', 'currency-dollar', 'chart-bar',
      'home', 'car', 'utensils', 'shopping-bag',
      'film', 'heart', 'academic-cap', 'briefcase',
      'gift', 'phone', 'computer-desktop', 'camera',
      'musical-note', 'book-open', 'puzzle-piece', 'star'
    ]
  }

  // ==========================================================================
  // 返回接口 - Return Interface
  // ==========================================================================

  return {
    // 响应式状态
    isLoading,
    hasError,
    errorMessage,
    hasCategories,

    // 计算属性
    categories,
    expenseCategories,
    incomeCategories,
    defaultCategories,
    customCategories,

    // 数据操作方法
    initialize,
    refresh,
    createCategory,
    updateCategory,
    deleteCategory,

    // 查询方法
    getCategoryById,
    getCategoriesByType,
    searchCategories,
    isCategoryNameExists,
    getCategoryUsageStats,
    getAllCategoryStats,

    // 排序方法
    updateCategoryOrder,
    updateCategoriesOrder,

    // 验证方法
    validateCategory,

    // 工具方法
    clearError,
    getNextOrder,
    getColorOptions,
    getIconOptions
  }
}
