<template>
  <div class="page-container">
    <div class="flex flex-col items-center justify-center min-h-screen px-4">
      <div class="text-center">
        <!-- 404 图标 -->
        <div class="mb-8">
          <div class="w-24 h-24 mx-auto bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center">
            <ExclamationTriangleIcon class="w-12 h-12 text-gray-400" />
          </div>
        </div>
        
        <!-- 错误信息 -->
        <h1 class="text-4xl font-bold text-gray-900 dark:text-gray-100 mb-4">
          404
        </h1>
        <h2 class="text-xl font-semibold text-gray-700 dark:text-gray-300 mb-2">
          页面未找到
        </h2>
        <p class="text-gray-500 dark:text-gray-400 mb-8 max-w-md">
          抱歉，您访问的页面不存在或已被移动。请检查网址是否正确，或返回首页。
        </p>
        
        <!-- 操作按钮 -->
        <div class="space-y-3">
          <BaseButton
            variant="primary"
            size="lg"
            full-width
            @click="goHome"
          >
            <HomeIcon class="w-5 h-5 mr-2" />
            返回首页
          </BaseButton>
          
          <BaseButton
            variant="ghost"
            size="lg"
            full-width
            @click="goBack"
          >
            <ArrowLeftIcon class="w-5 h-5 mr-2" />
            返回上页
          </BaseButton>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import {
  ExclamationTriangleIcon,
  HomeIcon,
  ArrowLeftIcon
} from '@heroicons/vue/24/outline'
import { BaseButton } from '@/components/ui'

const router = useRouter()

const goHome = () => {
  router.push('/')
}

const goBack = () => {
  if (window.history.length > 1) {
    router.back()
  } else {
    router.push('/')
  }
}
</script>
