<template>
  <nav class="fixed bottom-0 left-0 right-0 bg-white dark:bg-black border-t border-gray-200 dark:border-gray-800 safe-area-bottom">
    <div class="flex items-center justify-around h-16">
      <router-link
        v-for="item in navItems"
        :key="item.name"
        :to="item.path"
        class="flex flex-col items-center justify-center flex-1 h-full"
        :class="[
          $route.name === item.name
            ? 'text-blue-500'
            : 'text-gray-500 dark:text-gray-400'
        ]"
      >
        <component
          :is="item.icon"
          class="w-7 h-7 transition-transform duration-200"
          :class="[
            $route.name === item.name ? 'scale-110' : 'scale-100'
          ]"
        />
      </router-link>
    </div>
  </nav>
</template>

<script setup lang="ts">
import {
  CurrencyDollarIcon,
  PlusCircleIcon,
  ChartBarIcon,
  Cog6ToothIcon
} from '@heroicons/vue/24/outline'
import {
  CurrencyDollarIcon as CurrencyDollarIconSolid,
  PlusCircleIcon as PlusCircleIconSolid,
  ChartBarIcon as ChartBarIconSolid,
  Cog6ToothIcon as Cog6ToothIconSolid
} from '@heroicons/vue/24/solid'
import { useRoute } from 'vue-router'
import { useI18n } from 'vue-i18n'

const route = useRoute()
const { t } = useI18n()

const navItems = [
  {
    name: 'Home',
    path: '/',
    labelKey: 'nav.track',
    icon: CurrencyDollarIcon,
    activeIcon: CurrencyDollarIconSolid
  },
  {
    name: 'Stats',
    path: '/stats',
    labelKey: 'nav.stats',
    icon: ChartBarIcon,
    activeIcon: ChartBarIconSolid
  },
  {
    name: 'Settings',
    path: '/settings',
    labelKey: 'nav.settings',
    icon: Cog6ToothIcon,
    activeIcon: Cog6ToothIconSolid
  }
]
</script>

<style scoped>
.router-link-active {
  @apply text-blue-500;
}

/* 移除触摸反馈效果，保持原生APP体验 */
</style>
