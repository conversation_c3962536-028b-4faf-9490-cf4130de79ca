<template>
  <Teleport to="body">
    <Transition name="modal" appear>
      <div
        v-if="modelValue"
        :class="backdropClasses"
        @click="handleBackdropClick"
      >
        <div
          :class="contentClasses"
          @click.stop
        >
          <!-- 头部 -->
          <div v-if="$slots.header || title || showClose" class="flex items-center justify-between p-6 pb-4">
            <div class="flex-1">
              <slot name="header">
                <h2 v-if="title" class="text-lg font-semibold text-gray-900 dark:text-gray-100">
                  {{ title }}
                </h2>
              </slot>
            </div>
            
            <button
              v-if="showClose"
              @click="close"
              class="ml-4 p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-200 transition-colors"
            >
              <XMarkIcon class="w-5 h-5" />
            </button>
          </div>
          
          <!-- 内容 -->
          <div :class="bodyClasses">
            <slot />
          </div>
          
          <!-- 底部 -->
          <div v-if="$slots.footer" class="p-6 pt-4 border-t border-gray-200 dark:border-gray-700">
            <slot name="footer" />
          </div>
        </div>
      </div>
    </Transition>
  </Teleport>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { XMarkIcon } from '@heroicons/vue/24/outline'

interface Props {
  modelValue: boolean
  title?: string
  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full'
  position?: 'center' | 'top' | 'bottom'
  showClose?: boolean
  closeOnBackdrop?: boolean
  persistent?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  size: 'md',
  position: 'center',
  showClose: true,
  closeOnBackdrop: true,
  persistent: false
})

const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  close: []
}>()

const backdropClasses = computed(() => [
  'fixed inset-0 bg-black bg-opacity-50 z-50 p-4',
  'flex',
  {
    'items-center justify-center': props.position === 'center',
    'items-start justify-center pt-16': props.position === 'top',
    'items-end justify-center': props.position === 'bottom'
  }
])

const contentClasses = computed(() => {
  const baseClasses = [
    'modal-content',
    'animate-scale-in'
  ]
  
  // Size classes
  switch (props.size) {
    case 'sm':
      baseClasses.push('max-w-sm')
      break
    case 'lg':
      baseClasses.push('max-w-2xl')
      break
    case 'xl':
      baseClasses.push('max-w-4xl')
      break
    case 'full':
      baseClasses.push('max-w-full w-full h-full rounded-none')
      break
    default:
      baseClasses.push('max-w-md')
  }
  
  // Position specific classes
  if (props.position === 'bottom') {
    baseClasses.push('rounded-t-2xl rounded-b-none', 'w-full', 'max-h-[80vh]', 'overflow-y-auto')
  }
  
  return baseClasses
})

const bodyClasses = computed(() => [
  'px-6',
  {
    'pb-6': !props.$slots?.footer,
    'max-h-96 overflow-y-auto': props.size !== 'full'
  }
])

const close = () => {
  if (!props.persistent) {
    emit('update:modelValue', false)
    emit('close')
  }
}

const handleBackdropClick = () => {
  if (props.closeOnBackdrop) {
    close()
  }
}
</script>
