<template>
  <div class="select-wrapper" :class="wrapperClasses">
    <!-- 标签 -->
    <label 
      v-if="label" 
      :for="selectId" 
      class="select-label"
      :class="labelClasses"
    >
      {{ label }}
      <span v-if="required" class="select-required">*</span>
    </label>
    
    <!-- 选择器容器 -->
    <div 
      class="select-container"
      :class="containerClasses"
    >
      <!-- 选择器触发器 -->
      <button
        :id="selectId"
        ref="triggerRef"
        type="button"
        :disabled="disabled"
        :aria-expanded="isOpen"
        :aria-haspopup="true"
        :aria-label="ariaLabel"
        :aria-describedby="ariaDescribedby"
        class="select-trigger"
        :class="triggerClasses"
        @click="toggleDropdown"
        @keydown="handleTriggerKeydown"
        @blur="handleTriggerBlur"
      >
        <!-- 选中值显示 -->
        <div class="select-value" :class="valueClasses">
          <template v-if="hasSelection">
            <!-- 多选显示 -->
            <div v-if="multiple && selectedOptions.length > 0" class="select-tags">
              <span
                v-for="option in displayedTags"
                :key="option.value"
                class="select-tag"
              >
                {{ option.label }}
                <button
                  type="button"
                  class="select-tag-close"
                  @click.stop="removeOption(option.value)"
                >
                  <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </span>
              <span v-if="remainingCount > 0" class="select-tag-more">
                +{{ remainingCount }}
              </span>
            </div>
            <!-- 单选显示 -->
            <div v-else-if="!multiple && selectedOption" class="select-single-value">
              <component 
                v-if="selectedOption.icon" 
                :is="selectedOption.icon" 
                class="select-option-icon" 
              />
              <span>{{ selectedOption.label }}</span>
            </div>
          </template>
          <!-- 占位符 -->
          <span v-else class="select-placeholder">
            {{ placeholder }}
          </span>
        </div>
        
        <!-- 下拉箭头 -->
        <div class="select-arrow" :class="{ 'select-arrow-open': isOpen }">
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
          </svg>
        </div>
        
        <!-- 清除按钮 -->
        <button
          v-if="clearable && hasSelection && !disabled"
          type="button"
          class="select-clear"
          @click.stop="handleClear"
        >
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </button>
      
      <!-- 下拉菜单 -->
      <Teleport to="body">
        <div
          v-if="isOpen"
          ref="dropdownRef"
          class="select-dropdown"
          :class="dropdownClasses"
          :style="dropdownStyle"
        >
          <!-- 搜索框 -->
          <div v-if="searchable" class="select-search">
            <input
              ref="searchRef"
              v-model="searchQuery"
              type="text"
              class="select-search-input"
              :placeholder="searchPlaceholder"
              @keydown="handleSearchKeydown"
            />
          </div>
          
          <!-- 选项列表 -->
          <div class="select-options" :class="optionsClasses">
            <!-- 加载状态 -->
            <div v-if="loading" class="select-loading">
              <div class="animate-spin w-4 h-4 border-2 border-current border-t-transparent rounded-full"></div>
              <span class="ml-2">加载中...</span>
            </div>
            
            <!-- 空状态 -->
            <div v-else-if="filteredOptions.length === 0" class="select-empty">
              {{ searchQuery ? '无匹配结果' : '暂无数据' }}
            </div>
            
            <!-- 选项 -->
            <template v-else>
              <div
                v-for="(option, index) in filteredOptions"
                :key="option.value"
                class="select-option"
                :class="getOptionClasses(option, index)"
                @click="selectOption(option)"
                @mouseenter="setHighlightedIndex(index)"
              >
                <!-- 选项图标 -->
                <component 
                  v-if="option.icon" 
                  :is="option.icon" 
                  class="select-option-icon" 
                />
                
                <!-- 选项内容 -->
                <div class="select-option-content">
                  <div class="select-option-label">{{ option.label }}</div>
                  <div v-if="option.description" class="select-option-description">
                    {{ option.description }}
                  </div>
                </div>
                
                <!-- 选中标记 -->
                <div v-if="isSelected(option)" class="select-option-check">
                  <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                  </svg>
                </div>
              </div>
            </template>
          </div>
        </div>
      </Teleport>
    </div>
    
    <!-- 错误信息 -->
    <div 
      v-if="error" 
      class="select-error-message"
      :id="`${selectId}-error`"
    >
      {{ error }}
    </div>
    
    <!-- 帮助文本 -->
    <div 
      v-else-if="helpText" 
      class="select-help-text"
      :id="`${selectId}-help`"
    >
      {{ helpText }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, nextTick, onMounted, onUnmounted, watch } from 'vue'
import type { SelectProps, SelectOption } from '@/types/components'

interface Props extends /* @vue-ignore */ SelectProps {
  /** 帮助文本 */
  helpText?: string
  /** 搜索占位符 */
  searchPlaceholder?: string
  /** 最大显示标签数 */
  maxTagCount?: number
  /** 无障碍标签 */
  ariaLabel?: string
  /** 无障碍描述 */
  ariaDescribedby?: string
}

const props = withDefaults(defineProps<Props>(), {
  size: 'medium',
  disabled: false,
  multiple: false,
  searchable: false,
  clearable: false,
  loading: false,
  placeholder: '请选择',
  searchPlaceholder: '搜索选项',
  maxTagCount: 3
})

const emit = defineEmits<{
  'update:value': [value: any]
  select: [value: any]
  search: [query: string]
  clear: []
  open: []
  close: []
}>()

// 响应式数据
const triggerRef = ref<HTMLButtonElement>()
const dropdownRef = ref<HTMLDivElement>()
const searchRef = ref<HTMLInputElement>()
const selectId = ref(`select-${Math.random().toString(36).substr(2, 9)}`)
const isOpen = ref(false)
const searchQuery = ref('')
const highlightedIndex = ref(-1)
const dropdownStyle = ref({})

// 计算属性
const selectedOptions = computed(() => {
  if (!props.value) return []
  
  const values = Array.isArray(props.value) ? props.value : [props.value]
  return props.options.filter(option => values.includes(option.value))
})

const selectedOption = computed(() => {
  if (props.multiple || !props.value) return null
  return props.options.find(option => option.value === props.value) || null
})

const hasSelection = computed(() => {
  if (props.multiple) {
    return selectedOptions.value.length > 0
  }
  return selectedOption.value !== null
})

const displayedTags = computed(() => {
  return selectedOptions.value.slice(0, props.maxTagCount)
})

const remainingCount = computed(() => {
  return Math.max(0, selectedOptions.value.length - props.maxTagCount)
})

const filteredOptions = computed(() => {
  if (!searchQuery.value) return props.options
  
  const query = searchQuery.value.toLowerCase()
  return props.options.filter(option => 
    option.label.toLowerCase().includes(query) ||
    (option.description && option.description.toLowerCase().includes(query))
  )
})

// 样式类计算
const wrapperClasses = computed(() => [
  'select-wrapper',
  {
    'select-wrapper-error': props.error,
    'select-wrapper-disabled': props.disabled
  }
])

const labelClasses = computed(() => [
  'select-label',
  `select-label-${props.size}`
])

const containerClasses = computed(() => [
  'select-container',
  `select-container-${props.size}`
])

const triggerClasses = computed(() => [
  'select-trigger',
  `select-trigger-${props.size}`,
  {
    'select-trigger-error': props.error,
    'select-trigger-disabled': props.disabled,
    'select-trigger-open': isOpen.value
  }
])

const valueClasses = computed(() => [
  'select-value',
  {
    'select-value-placeholder': !hasSelection.value
  }
])

const dropdownClasses = computed(() => [
  'select-dropdown',
  `select-dropdown-${props.size}`
])

const optionsClasses = computed(() => [
  'select-options',
  {
    'select-options-searchable': props.searchable
  }
])

// 方法
const toggleDropdown = () => {
  if (props.disabled) return
  
  if (isOpen.value) {
    closeDropdown()
  } else {
    openDropdown()
  }
}

const openDropdown = async () => {
  isOpen.value = true
  emit('open')
  
  await nextTick()
  updateDropdownPosition()
  
  if (props.searchable) {
    searchRef.value?.focus()
  }
}

const closeDropdown = () => {
  isOpen.value = false
  searchQuery.value = ''
  highlightedIndex.value = -1
  emit('close')
}

const updateDropdownPosition = () => {
  if (!triggerRef.value || !dropdownRef.value) return
  
  const triggerRect = triggerRef.value.getBoundingClientRect()
  const dropdownRect = dropdownRef.value.getBoundingClientRect()
  const viewportHeight = window.innerHeight
  
  const spaceBelow = viewportHeight - triggerRect.bottom
  const spaceAbove = triggerRect.top
  
  let top = triggerRect.bottom + window.scrollY
  
  // 如果下方空间不足且上方空间更大，则向上展开
  if (spaceBelow < dropdownRect.height && spaceAbove > spaceBelow) {
    top = triggerRect.top + window.scrollY - dropdownRect.height
  }
  
  dropdownStyle.value = {
    position: 'absolute',
    top: `${top}px`,
    left: `${triggerRect.left + window.scrollX}px`,
    width: `${triggerRect.width}px`,
    zIndex: 1000
  }
}

const selectOption = (option: SelectOption) => {
  if (option.disabled) return
  
  let newValue: any
  
  if (props.multiple) {
    const currentValues = Array.isArray(props.value) ? props.value : []
    if (currentValues.includes(option.value)) {
      newValue = currentValues.filter(v => v !== option.value)
    } else {
      if (props.maxCount && currentValues.length >= props.maxCount) {
        return
      }
      newValue = [...currentValues, option.value]
    }
  } else {
    newValue = option.value
    closeDropdown()
  }
  
  emit('update:value', newValue)
  emit('select', newValue)
}

const removeOption = (value: any) => {
  if (props.disabled) return
  
  const currentValues = Array.isArray(props.value) ? props.value : []
  const newValue = currentValues.filter(v => v !== value)
  
  emit('update:value', newValue)
  emit('select', newValue)
}

const handleClear = () => {
  const newValue = props.multiple ? [] : null
  emit('update:value', newValue)
  emit('clear')
}

const isSelected = (option: SelectOption) => {
  if (props.multiple) {
    const values = Array.isArray(props.value) ? props.value : []
    return values.includes(option.value)
  }
  return props.value === option.value
}

const setHighlightedIndex = (index: number) => {
  highlightedIndex.value = index
}

const getOptionClasses = (option: SelectOption, index: number) => [
  'select-option',
  {
    'select-option-selected': isSelected(option),
    'select-option-disabled': option.disabled,
    'select-option-highlighted': index === highlightedIndex.value
  }
]

// 键盘事件处理
const handleTriggerKeydown = (event: KeyboardEvent) => {
  switch (event.key) {
    case 'Enter':
    case ' ':
      event.preventDefault()
      toggleDropdown()
      break
    case 'ArrowDown':
      event.preventDefault()
      if (!isOpen.value) {
        openDropdown()
      }
      break
    case 'Escape':
      if (isOpen.value) {
        closeDropdown()
      }
      break
  }
}

const handleTriggerBlur = (event: FocusEvent) => {
  // 延迟关闭，允许点击下拉选项
  setTimeout(() => {
    if (!dropdownRef.value?.contains(event.relatedTarget as Node)) {
      closeDropdown()
    }
  }, 100)
}

const handleSearchKeydown = (event: KeyboardEvent) => {
  switch (event.key) {
    case 'ArrowDown':
      event.preventDefault()
      highlightedIndex.value = Math.min(highlightedIndex.value + 1, filteredOptions.value.length - 1)
      break
    case 'ArrowUp':
      event.preventDefault()
      highlightedIndex.value = Math.max(highlightedIndex.value - 1, 0)
      break
    case 'Enter':
      event.preventDefault()
      if (highlightedIndex.value >= 0 && filteredOptions.value[highlightedIndex.value]) {
        selectOption(filteredOptions.value[highlightedIndex.value])
      }
      break
    case 'Escape':
      closeDropdown()
      break
  }
}

// 监听搜索查询变化
watch(searchQuery, (newQuery) => {
  emit('search', newQuery)
  highlightedIndex.value = -1
})

// 点击外部关闭
const handleClickOutside = (event: Event) => {
  if (
    isOpen.value &&
    !triggerRef.value?.contains(event.target as Node) &&
    !dropdownRef.value?.contains(event.target as Node)
  ) {
    closeDropdown()
  }
}

// 生命周期
onMounted(() => {
  document.addEventListener('click', handleClickOutside)
  window.addEventListener('resize', updateDropdownPosition)
  window.addEventListener('scroll', updateDropdownPosition)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
  window.removeEventListener('resize', updateDropdownPosition)
  window.removeEventListener('scroll', updateDropdownPosition)
})

// 暴露方法
defineExpose({
  open: openDropdown,
  close: closeDropdown,
  focus: () => triggerRef.value?.focus()
})
</script>

<style scoped>
/* 选择器包装器 */
.select-wrapper {
  @apply space-y-1;
}

.select-wrapper-error {
  /* 错误状态样式 */
}

.select-wrapper-disabled {
  @apply opacity-60;
}

/* 标签样式 */
.select-label {
  @apply block font-medium text-gray-700 dark:text-gray-300;
}

.select-label-small {
  @apply text-xs;
}

.select-label-medium {
  @apply text-sm;
}

.select-label-large {
  @apply text-base;
}

.select-required {
  @apply text-red-500 ml-1;
}

/* 选择器容器 */
.select-container {
  @apply relative;
}

/* 选择器触发器 */
.select-trigger {
  @apply w-full flex items-center justify-between;
  @apply border border-gray-300 rounded-lg bg-white text-gray-900;
  @apply transition-all duration-200 cursor-pointer;
  @apply focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent;
  @apply dark:bg-gray-800 dark:border-gray-600 dark:text-gray-100;
}

.select-trigger-small {
  @apply px-3 py-1.5 text-sm;
}

.select-trigger-medium {
  @apply px-4 py-2 text-base;
}

.select-trigger-large {
  @apply px-4 py-3 text-lg;
}

.select-trigger-error {
  @apply border-red-300 focus:ring-red-500 focus:border-red-500;
  @apply dark:border-red-600;
}

.select-trigger-disabled {
  @apply bg-gray-50 cursor-not-allowed;
  @apply dark:bg-gray-900;
}

.select-trigger-open {
  @apply ring-2 ring-blue-500 border-transparent;
  @apply dark:ring-blue-400;
}

/* 选中值显示 */
.select-value {
  @apply flex-1 flex items-center min-w-0;
}

.select-value-placeholder {
  @apply text-gray-400 dark:text-gray-500;
}

/* 标签显示 */
.select-tags {
  @apply flex flex-wrap gap-1;
}

.select-tag {
  @apply inline-flex items-center px-2 py-1 text-xs font-medium;
  @apply bg-blue-100 text-blue-800 rounded-md;
  @apply dark:bg-blue-900 dark:text-blue-200;
}

.select-tag-close {
  @apply ml-1 text-blue-600 hover:text-blue-800;
  @apply dark:text-blue-300 dark:hover:text-blue-100;
  @apply focus:outline-none;
}

.select-tag-more {
  @apply inline-flex items-center px-2 py-1 text-xs font-medium;
  @apply bg-gray-100 text-gray-600 rounded-md;
  @apply dark:bg-gray-700 dark:text-gray-300;
}

/* 单选值显示 */
.select-single-value {
  @apply flex items-center;
}

/* 占位符 */
.select-placeholder {
  @apply text-gray-400 dark:text-gray-500;
}

/* 下拉箭头 */
.select-arrow {
  @apply ml-2 text-gray-400 transition-transform duration-200;
  @apply dark:text-gray-500;
}

.select-arrow-open {
  @apply transform rotate-180;
}

/* 清除按钮 */
.select-clear {
  @apply ml-2 p-1 text-gray-400 hover:text-gray-600 transition-colors;
  @apply dark:text-gray-500 dark:hover:text-gray-300;
  @apply focus:outline-none focus:ring-2 focus:ring-blue-500 rounded;
}

/* 下拉菜单 */
.select-dropdown {
  @apply bg-white border border-gray-200 rounded-lg shadow-lg;
  @apply dark:bg-gray-800 dark:border-gray-600;
  @apply max-h-60 overflow-hidden;
}

/* 搜索框 */
.select-search {
  @apply p-2 border-b border-gray-200 dark:border-gray-600;
}

.select-search-input {
  @apply w-full px-3 py-2 text-sm border border-gray-300 rounded-md;
  @apply focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent;
  @apply dark:bg-gray-700 dark:border-gray-600 dark:text-gray-100;
}

/* 选项列表 */
.select-options {
  @apply overflow-y-auto;
}

.select-options-searchable {
  @apply max-h-48;
}

/* 选项 */
.select-option {
  @apply flex items-center px-3 py-2 cursor-pointer transition-colors;
  @apply hover:bg-gray-50 dark:hover:bg-gray-700;
}

.select-option-selected {
  @apply bg-blue-50 text-blue-700;
  @apply dark:bg-blue-900 dark:text-blue-200;
}

.select-option-disabled {
  @apply opacity-50 cursor-not-allowed;
}

.select-option-highlighted {
  @apply bg-gray-100 dark:bg-gray-600;
}

/* 选项图标 */
.select-option-icon {
  @apply w-5 h-5 mr-3 text-gray-400;
}

/* 选项内容 */
.select-option-content {
  @apply flex-1 min-w-0;
}

.select-option-label {
  @apply text-sm font-medium text-gray-900 dark:text-gray-100;
}

.select-option-description {
  @apply text-xs text-gray-500 dark:text-gray-400 truncate;
}

/* 选中标记 */
.select-option-check {
  @apply ml-2 text-blue-600 dark:text-blue-400;
}

/* 加载状态 */
.select-loading {
  @apply flex items-center justify-center px-3 py-4 text-sm text-gray-500;
  @apply dark:text-gray-400;
}

/* 空状态 */
.select-empty {
  @apply px-3 py-4 text-sm text-gray-500 text-center;
  @apply dark:text-gray-400;
}

/* 错误信息 */
.select-error-message {
  @apply text-sm text-red-600 dark:text-red-400;
}

/* 帮助文本 */
.select-help-text {
  @apply text-sm text-gray-500 dark:text-gray-400;
}
</style>
