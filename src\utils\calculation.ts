/**
 * 计算工具函数
 * Calculation utility functions
 */

import type { Transaction, TransactionType } from '@/types'

// ==========================================================================
// 基础数学计算 - Basic Math Calculations
// ==========================================================================

/**
 * 安全的加法运算（避免浮点数精度问题）
 * @param a 加数
 * @param b 被加数
 * @returns 和
 */
export function safeAdd(a: number, b: number): number {
  const factor = Math.pow(10, Math.max(getDecimalPlaces(a), getDecimalPlaces(b)))
  return Math.round((a * factor + b * factor)) / factor
}

/**
 * 安全的减法运算
 * @param a 被减数
 * @param b 减数
 * @returns 差
 */
export function safeSubtract(a: number, b: number): number {
  const factor = Math.pow(10, Math.max(getDecimalPlaces(a), getDecimalPlaces(b)))
  return Math.round((a * factor - b * factor)) / factor
}

/**
 * 安全的乘法运算
 * @param a 乘数
 * @param b 被乘数
 * @returns 积
 */
export function safeMultiply(a: number, b: number): number {
  const factorA = Math.pow(10, getDecimalPlaces(a))
  const factorB = Math.pow(10, getDecimalPlaces(b))
  return Math.round((a * factorA) * (b * factorB)) / (factorA * factorB)
}

/**
 * 安全的除法运算
 * @param a 被除数
 * @param b 除数
 * @returns 商
 */
export function safeDivide(a: number, b: number): number {
  if (b === 0) {
    throw new Error('Division by zero')
  }
  const factorA = Math.pow(10, getDecimalPlaces(a))
  const factorB = Math.pow(10, getDecimalPlaces(b))
  return (a * factorA) / (b * factorB)
}

/**
 * 获取数字的小数位数
 * @param num 数字
 * @returns 小数位数
 */
export function getDecimalPlaces(num: number): number {
  if (Math.floor(num) === num) return 0
  const str = num.toString()
  if (str.indexOf('.') !== -1 && str.indexOf('e-') === -1) {
    return str.split('.')[1].length
  } else if (str.indexOf('e-') !== -1) {
    const parts = str.split('e-')
    return parseInt(parts[1], 10)
  }
  return 0
}

/**
 * 四舍五入到指定小数位
 * @param num 数字
 * @param decimals 小数位数
 * @returns 四舍五入后的数字
 */
export function roundToDecimals(num: number, decimals: number): number {
  const factor = Math.pow(10, decimals)
  return Math.round(num * factor) / factor
}

/**
 * 计算百分比
 * @param value 值
 * @param total 总数
 * @returns 百分比（0-1之间）
 */
export function calculatePercentage(value: number, total: number): number {
  if (total === 0) return 0
  return safeDivide(value, total)
}

/**
 * 计算增长率
 * @param current 当前值
 * @param previous 之前值
 * @returns 增长率（0-1之间，正数表示增长，负数表示下降）
 */
export function calculateGrowthRate(current: number, previous: number): number {
  if (previous === 0) return current > 0 ? 1 : 0
  return safeDivide(safeSubtract(current, previous), previous)
}

// ==========================================================================
// 统计计算 - Statistical Calculations
// ==========================================================================

/**
 * 计算平均值
 * @param numbers 数字数组
 * @returns 平均值
 */
export function calculateAverage(numbers: number[]): number {
  if (numbers.length === 0) return 0
  const sum = numbers.reduce((acc, num) => safeAdd(acc, num), 0)
  return safeDivide(sum, numbers.length)
}

/**
 * 计算中位数
 * @param numbers 数字数组
 * @returns 中位数
 */
export function calculateMedian(numbers: number[]): number {
  if (numbers.length === 0) return 0
  
  const sorted = [...numbers].sort((a, b) => a - b)
  const middle = Math.floor(sorted.length / 2)
  
  if (sorted.length % 2 === 0) {
    return safeDivide(safeAdd(sorted[middle - 1], sorted[middle]), 2)
  } else {
    return sorted[middle]
  }
}

/**
 * 计算众数
 * @param numbers 数字数组
 * @returns 众数数组
 */
export function calculateMode(numbers: number[]): number[] {
  if (numbers.length === 0) return []
  
  const frequency: Record<number, number> = {}
  let maxFreq = 0
  
  // 计算频率
  numbers.forEach(num => {
    frequency[num] = (frequency[num] || 0) + 1
    maxFreq = Math.max(maxFreq, frequency[num])
  })
  
  // 找出所有众数
  return Object.keys(frequency)
    .filter(key => frequency[Number(key)] === maxFreq)
    .map(Number)
}

/**
 * 计算标准差
 * @param numbers 数字数组
 * @returns 标准差
 */
export function calculateStandardDeviation(numbers: number[]): number {
  if (numbers.length === 0) return 0
  
  const avg = calculateAverage(numbers)
  const squaredDiffs = numbers.map(num => Math.pow(safeSubtract(num, avg), 2))
  const avgSquaredDiff = calculateAverage(squaredDiffs)
  
  return Math.sqrt(avgSquaredDiff)
}

/**
 * 计算方差
 * @param numbers 数字数组
 * @returns 方差
 */
export function calculateVariance(numbers: number[]): number {
  const stdDev = calculateStandardDeviation(numbers)
  return Math.pow(stdDev, 2)
}

// ==========================================================================
// 交易数据计算 - Transaction Calculations
// ==========================================================================

/**
 * 计算交易总额
 * @param transactions 交易记录数组
 * @param type 交易类型（可选）
 * @returns 总额
 */
export function calculateTransactionTotal(
  transactions: Transaction[],
  type?: TransactionType
): number {
  return transactions
    .filter(t => !type || t.type === type)
    .reduce((sum, t) => safeAdd(sum, t.amount), 0)
}

/**
 * 计算收支平衡
 * @param transactions 交易记录数组
 * @returns 平衡值（正数表示盈余，负数表示亏损）
 */
export function calculateBalance(transactions: Transaction[]): number {
  const income = calculateTransactionTotal(transactions, 'income')
  const expense = calculateTransactionTotal(transactions, 'expense')
  return safeSubtract(income, expense)
}

/**
 * 计算平均交易金额
 * @param transactions 交易记录数组
 * @param type 交易类型（可选）
 * @returns 平均金额
 */
export function calculateAverageTransactionAmount(
  transactions: Transaction[],
  type?: TransactionType
): number {
  const filteredTransactions = transactions.filter(t => !type || t.type === type)
  if (filteredTransactions.length === 0) return 0
  
  const total = calculateTransactionTotal(filteredTransactions)
  return safeDivide(total, filteredTransactions.length)
}

/**
 * 计算交易频率（每天平均交易次数）
 * @param transactions 交易记录数组
 * @param startDate 开始日期
 * @param endDate 结束日期
 * @returns 每天平均交易次数
 */
export function calculateTransactionFrequency(
  transactions: Transaction[],
  startDate: string,
  endDate: string
): number {
  const start = new Date(startDate)
  const end = new Date(endDate)
  const daysDiff = Math.ceil((end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24)) + 1
  
  if (daysDiff <= 0) return 0
  
  const filteredTransactions = transactions.filter(t => 
    t.date >= startDate && t.date <= endDate
  )
  
  return safeDivide(filteredTransactions.length, daysDiff)
}

/**
 * 计算月度支出趋势
 * @param transactions 交易记录数组
 * @param months 月份数
 * @returns 趋势数据
 */
export function calculateMonthlyExpenseTrend(
  transactions: Transaction[],
  months: number = 6
): Array<{
  month: string
  amount: number
  change: number
  changePercent: number
}> {
  const result: Array<{
    month: string
    amount: number
    change: number
    changePercent: number
  }> = []
  
  const currentDate = new Date()
  
  for (let i = months - 1; i >= 0; i--) {
    const date = new Date(currentDate)
    date.setMonth(date.getMonth() - i)
    const monthString = date.toISOString().substring(0, 7)
    
    const monthTransactions = transactions.filter(t => 
      t.date.startsWith(monthString) && t.type === 'expense'
    )
    
    const amount = calculateTransactionTotal(monthTransactions)
    
    let change = 0
    let changePercent = 0
    
    if (result.length > 0) {
      const previousAmount = result[result.length - 1].amount
      change = safeSubtract(amount, previousAmount)
      changePercent = calculateGrowthRate(amount, previousAmount)
    }
    
    result.push({
      month: monthString,
      amount,
      change,
      changePercent
    })
  }
  
  return result
}

/**
 * 计算分类支出占比
 * @param transactions 交易记录数组
 * @param type 交易类型
 * @returns 分类占比数据
 */
export function calculateCategoryPercentages(
  transactions: Transaction[],
  type: TransactionType
): Array<{
  categoryId: string
  amount: number
  percentage: number
  count: number
}> {
  const filteredTransactions = transactions.filter(t => t.type === type)
  const total = calculateTransactionTotal(filteredTransactions)
  
  if (total === 0) return []
  
  // 按分类分组
  const categoryGroups: Record<string, Transaction[]> = {}
  filteredTransactions.forEach(t => {
    if (!categoryGroups[t.categoryId]) {
      categoryGroups[t.categoryId] = []
    }
    categoryGroups[t.categoryId].push(t)
  })
  
  // 计算每个分类的占比
  return Object.entries(categoryGroups).map(([categoryId, categoryTransactions]) => {
    const amount = calculateTransactionTotal(categoryTransactions)
    const percentage = calculatePercentage(amount, total)
    
    return {
      categoryId,
      amount,
      percentage,
      count: categoryTransactions.length
    }
  }).sort((a, b) => b.amount - a.amount)
}

// ==========================================================================
// 预算计算 - Budget Calculations
// ==========================================================================

/**
 * 计算预算使用率
 * @param spent 已花费金额
 * @param budget 预算金额
 * @returns 使用率（0-1之间）
 */
export function calculateBudgetUsage(spent: number, budget: number): number {
  if (budget <= 0) return 0
  return Math.min(calculatePercentage(spent, budget), 1)
}

/**
 * 计算预算剩余
 * @param spent 已花费金额
 * @param budget 预算金额
 * @returns 剩余金额
 */
export function calculateBudgetRemaining(spent: number, budget: number): number {
  return Math.max(safeSubtract(budget, spent), 0)
}

/**
 * 计算预算超支
 * @param spent 已花费金额
 * @param budget 预算金额
 * @returns 超支金额（负数表示未超支）
 */
export function calculateBudgetOverspend(spent: number, budget: number): number {
  return Math.max(safeSubtract(spent, budget), 0)
}

/**
 * 计算日均可花费金额
 * @param remaining 剩余预算
 * @param daysLeft 剩余天数
 * @returns 日均可花费金额
 */
export function calculateDailySpendingLimit(remaining: number, daysLeft: number): number {
  if (daysLeft <= 0) return 0
  return safeDivide(remaining, daysLeft)
}

// ==========================================================================
// 投资收益计算 - Investment Return Calculations
// ==========================================================================

/**
 * 计算简单利息
 * @param principal 本金
 * @param rate 利率（年化）
 * @param time 时间（年）
 * @returns 利息
 */
export function calculateSimpleInterest(principal: number, rate: number, time: number): number {
  return safeMultiply(safeMultiply(principal, rate), time)
}

/**
 * 计算复合利息
 * @param principal 本金
 * @param rate 利率（年化）
 * @param time 时间（年）
 * @param compoundFrequency 复利频率（每年复利次数）
 * @returns 最终金额
 */
export function calculateCompoundInterest(
  principal: number,
  rate: number,
  time: number,
  compoundFrequency: number = 1
): number {
  const ratePerPeriod = safeDivide(rate, compoundFrequency)
  const totalPeriods = safeMultiply(compoundFrequency, time)
  return safeMultiply(principal, Math.pow(safeAdd(1, ratePerPeriod), totalPeriods))
}

/**
 * 计算年化收益率
 * @param initialValue 初始值
 * @param finalValue 最终值
 * @param years 年数
 * @returns 年化收益率
 */
export function calculateAnnualizedReturn(
  initialValue: number,
  finalValue: number,
  years: number
): number {
  if (initialValue <= 0 || years <= 0) return 0
  return Math.pow(safeDivide(finalValue, initialValue), safeDivide(1, years)) - 1
}
