// UI组件导出
export { default as BaseButton } from './BaseButton.vue'
export { default as BaseInput } from './BaseInput.vue'
export { default as BaseSelect } from './BaseSelect.vue'
export { default as BaseTextarea } from './BaseTextarea.vue'
export { default as BaseNotification } from './BaseNotification.vue'
export { default as BaseModal } from './BaseModal.vue'
export { default as BaseCard } from './BaseCard.vue'
export { default as NumberKeypad } from './NumberKeypad.vue'
export { default as CategoryIcon } from './CategoryIcon.vue'

// 从类型文件重新导出组件相关类型
export type {
  // 按钮相关
  ButtonProps,
  ButtonType,
  ButtonVariant,
  ButtonColor,
  ButtonSize,

  // 输入框相关
  InputProps,
  InputType,
  InputSize,

  // 选择器相关
  SelectProps,
  SelectOption,
  SelectSize,

  // 文本域相关
  TextareaProps,
  TextareaSize,

  // 通知相关
  NotificationProps,
  NotificationAction,
  NotificationType,
  NotificationPosition,

  // 模态框相关
  ModalProps,
  ModalSize,
  ModalPosition,

  // 卡片相关
  CardProps,
  CardVariant,
  CardPadding,

  // 通用组件类型
  ComponentVariant,
  ComponentSize,
  ComponentColor
} from '@/types/components'

// 兼容性类型导出（保持向后兼容）
export type ButtonVariantLegacy = 'primary' | 'secondary' | 'success' | 'danger' | 'ghost'
export type ButtonSizeLegacy = 'sm' | 'md' | 'lg'
export type InputTypeLegacy = 'text' | 'number' | 'email' | 'password' | 'tel' | 'url'
export type ModalSizeLegacy = 'sm' | 'md' | 'lg' | 'xl' | 'full'
export type ModalPositionLegacy = 'center' | 'top' | 'bottom'
export type CardVariantLegacy = 'default' | 'elevated' | 'interactive'
export type CardPaddingLegacy = 'none' | 'sm' | 'md' | 'lg'
